{"cells": [{"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["C:\\Users\\<USER>\\OneDrive - 國立陽明交通大學\\文件\\academic\\專題\\env\\rl-environment\n"]}], "source": ["%cd C:\\Users\\<USER>\\OneDrive - 國立陽明交通大學\\文件\\academic\\專題\\env\\rl-environment"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["match_1 = pd.read_excel('match_data.xlsx')\n", "match_2 = pd.read_excel('match_data_1.xlsx')"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='ball_location_x', ylabel='Count'>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ball_location_x_diff = match_2['ball_location_x'] - match_1['ball_location_x']\n", "sns.histplot(ball_location_x_diff)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='ball_location_y', ylabel='Count'>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ball_location_y_diff = match_2['ball_location_y'] - match_1['ball_location_y']\n", "sns.histplot(ball_location_y_diff)\n"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='ball_height', ylabel='Count'>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ball_height_diff = match_2['ball_height'] - match_1['ball_height']\n", "sns.histplot(ball_height_diff)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='playerA_location_x', ylabel='Count'>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["playerA_location_X = match_2['playerA_location_x'] - match_1['playerA_location_x']\n", "sns.histplot(playerA_location_X)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='playerA_location_y', ylabel='Count'>"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["playerA_location_y = match_2['playerA_location_y'] - match_1['playerA_location_y']\n", "sns.histplot(playerA_location_y)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='playerB_location_x', ylabel='Count'>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["playerB_location_X = match_2['playerB_location_x'] - match_1['playerB_location_x']\n", "sns.histplot(playerB_location_X)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["<AxesSubplot:xlabel='playerB_location_y', ylabel='Count'>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["playerB_location_y = match_2['playerB_location_y'] - match_1['playerB_location_y']\n", "sns.histplot(playerB_location_y)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.10.4 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "df95319d8ce4e1d89f5365ae10992bc1f65da593082b1d264e8f529830ec2f02"}}}, "nbformat": 4, "nbformat_minor": 2}