{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     rally  server  landing_x  landing_y  player_location_x  \\\n", "0        1       1     -80.77     300.60              17.44   \n", "1        1       2      92.61     103.66              58.57   \n", "2        1       2     -97.17     293.16             -58.02   \n", "3        1       2      83.93     296.29              76.59   \n", "4        1       2      50.11     119.74             -54.85   \n", "5        1       2     -85.51      82.10              30.02   \n", "6        1       2      83.80      41.10              64.26   \n", "7        1       3      13.66     316.99             -80.76   \n", "8        2       1        NaN        NaN                NaN   \n", "9        2       2     -93.78     267.22             -53.89   \n", "10       2       2      94.00     341.33              76.27   \n", "11       2       3    -127.55     166.15             -69.68   \n", "12       3       1      22.46     307.30              -4.17   \n", "13       3       2     -81.51     261.14             -35.86   \n", "14       3       2      87.86     183.88              73.73   \n", "15       3       2     -75.37     112.63               2.25   \n", "16       3       2     -37.90     264.12              48.65   \n", "17       3       2     106.80     201.76              12.96   \n", "18       3       2     -77.86      44.79               5.77   \n", "19       3       2     -35.41     329.18              53.81   \n", "20       3       2      27.00     258.24              24.72   \n", "21       3       2      80.22     175.29             -15.22   \n", "22       3       2      45.18      66.80               3.33   \n", "23       3       2    -101.52     310.51              -9.21   \n", "24       3       2     106.45     256.24              70.24   \n", "25       3       2     -77.90      79.85             -55.54   \n", "26       3       2      76.53      68.22              68.59   \n", "27       3       2     -97.21     341.75             -65.69   \n", "28       3       2     104.33     163.90              46.00   \n", "29       3       3    -106.16      80.23             -67.81   \n", "30       4       1     -50.24      79.48               3.30   \n", "31       4       2      80.79     257.80              51.61   \n", "32       4       2    -100.70     307.70             -12.34   \n", "33       4       3     133.73     -44.92              63.02   \n", "34       5       1      28.25      93.56              -6.07   \n", "35       5       2     -83.20      62.90             -40.12   \n", "36       5       3      87.74     -11.29              71.08   \n", "37       6       1        NaN        NaN                NaN   \n", "38       6       2       6.13      72.33              37.87   \n", "39       6       2      23.30     285.84              -0.99   \n", "40       6       2    -100.25     230.16             -23.57   \n", "41       6       2      71.39     203.28              71.60   \n", "42       6       2     -74.06      84.10             -20.02   \n", "43       6       2     -56.24     289.02              62.68   \n", "44       6       2      98.96     285.08              34.28   \n", "45       6       2     -78.78     216.19             -38.97   \n", "46       6       2    -101.83     151.86              47.68   \n", "47       6       2      90.44     263.07              66.18   \n", "48       6       2     -98.82     321.73             -19.03   \n", "49       6       2      77.44      80.50              79.41   \n", "50       6       2    -116.32     305.17             -46.47   \n", "51       6       2      92.16      60.93              66.07   \n", "52       6       2     -92.81     307.57             -72.51   \n", "53       6       2    -117.41      54.32              71.56   \n", "54       6       3     134.26     334.86              80.18   \n", "55       7       1        NaN        NaN                NaN   \n", "56       7       2     -97.06     101.85             -66.03   \n", "57       7       2      92.85     273.61              71.14   \n", "58       7       2     -88.79     240.29             -38.58   \n", "59       7       3    -113.19     132.84              66.58   \n", "60       8       1        NaN        NaN                NaN   \n", "61       8       2    -108.29     207.53             -53.88   \n", "62       8       2      88.89      62.76              79.78   \n", "63       8       3    -103.28      -5.16             -70.47   \n", "64       9       1        NaN        NaN                NaN   \n", "65       9       2    -101.45     285.65             -69.94   \n", "66       9       2      93.44     207.45              81.30   \n", "67       9       2     -88.95     269.25             -46.71   \n", "68       9       3    -138.04     223.83              89.50   \n", "69      10       1        NaN        NaN                NaN   \n", "70      10       2      90.97      71.59              71.36   \n", "71      10       2    -107.12     305.02             -68.51   \n", "72      10       2      88.08     264.42              73.63   \n", "73      10       3    -107.60      75.97             -43.87   \n", "74      11       1      48.48      91.11              -9.01   \n", "75      11       2    -113.88     284.34             -56.10   \n", "76      11       3     -44.60       3.81              79.58   \n", "77      12       1     -82.11     306.85              17.46   \n", "78      12       2      97.47     211.89              62.83   \n", "79      12       2      68.80     119.55             -14.18   \n", "80      12       2     -83.32     315.82             -39.83   \n", "81      12       2    -108.43     290.70              73.95   \n", "82      12       2      89.93     108.12              72.50   \n", "83      12       2    -105.30      54.18             -69.78   \n", "84      12       2      70.25     296.52              74.37   \n", "85      12       2      71.77     310.65             -35.39   \n", "86      12       2      66.69     126.41             -35.40   \n", "87      12       2      63.28      84.13             -20.83   \n", "88      12       2      49.47     299.63             -40.77   \n", "89      12       3      24.64     -25.29             -14.58   \n", "90      13       1     -23.98      94.27               8.76   \n", "91      13       2      94.77     283.99              29.77   \n", "92      13       2     -92.77     293.72             -30.22   \n", "93      13       2    -106.19     119.71              93.14   \n", "94      13       2     109.82     134.93              61.31   \n", "95      13       2      79.15      48.46             -72.40   \n", "96      13       2     -88.06     290.90             -49.51   \n", "97      13       2      59.58     168.96              75.71   \n", "98      13       2     -99.42     151.82             -18.55   \n", "99      13       2     -74.74     276.26              69.18   \n", "100     13       2      71.03     117.32              53.25   \n", "101     13       2      68.05     290.61             -54.64   \n", "102     13       2      70.69     291.01             -41.49   \n", "103     13       2     114.15     127.28             -43.01   \n", "104     13       2    -120.49      86.88             -14.03   \n", "105     13       3      58.67     337.30              93.43   \n", "106     14       1      30.32     111.17              -8.02   \n", "107     14       2     -14.75     186.34             -51.38   \n", "108     14       3      42.60     313.69              12.93   \n", "109     15       1     -28.98     114.23               7.35   \n", "110     15       2     -90.99     293.11              42.74   \n", "111     15       2      82.77     229.86              60.48   \n", "112     15       2      68.16     101.19             -43.39   \n", "113     15       2    -103.21     293.89             -44.00   \n", "114     15       3      80.32     339.51              92.19   \n", "115     16       1        NaN        NaN                NaN   \n", "116     16       2      72.75     168.83              63.59   \n", "117     16       2     -94.76     133.25             -13.28   \n", "118     16       3    -144.83     308.56              72.92   \n", "119     17       1        NaN        NaN                NaN   \n", "120     17       2      56.75      78.98              72.97   \n", "121     17       2      70.25     296.52             -34.76   \n", "122     17       2     -82.98     273.71             -43.72   \n", "123     17       2     -84.66     301.87              72.28   \n", "124     17       2     106.20     242.64              73.95   \n", "125     17       2    -114.56     319.16             -66.97   \n", "126     17       2      66.29     247.09             111.23   \n", "127     17       2    -118.83     313.60             -35.19   \n", "128     17       2      89.34      46.53              86.89   \n", "129     17       2     -96.54     291.04             -71.39   \n", "130     17       2     -74.48     165.63              72.64   \n", "131     17       2      98.93      92.60              10.63   \n", "132     17       3      86.81     332.36             -75.25   \n", "133     18       1      34.63      93.48              -8.49   \n", "134     18       2    -102.18      30.32             -39.11   \n", "135     18       2     -19.70     289.77              63.35   \n", "136     18       2     -38.09     210.08              17.02   \n", "137     18       3      13.87     -19.90              22.54   \n", "138     19       1      46.75     302.14              -3.54   \n", "139     19       2     -96.32     299.29             -46.05   \n", "140     19       2     -66.03     280.09              93.00   \n", "141     19       2      98.91     121.43              47.30   \n", "142     19       2    -116.95      19.24             -80.06   \n", "143     19       2     105.42     295.93              83.78   \n", "144     19       2    -103.34      96.23             -68.09   \n", "145     19       2     -64.92     146.66              44.54   \n", "146     19       2      98.62     263.00              22.35   \n", "147     19       2    -115.75     327.70             -34.16   \n", "148     19       2      92.67     231.66             102.14   \n", "149     19       2    -120.33      84.72             -47.28   \n", "150     19       2      83.29     301.86             103.24   \n", "151     19       2      80.19     285.39             -74.08   \n", "152     19       2    -103.05     187.16             -28.79   \n", "153     19       3      93.75      -1.75              56.22   \n", "154     20       1     -81.10     315.47              15.03   \n", "155     20       2      80.62      97.22              55.83   \n", "156     20       2     -85.70      37.54             -56.58   \n", "157     20       2      74.84     110.56              63.36   \n", "158     20       3     107.56     214.53             -49.86   \n", "159     21       1        NaN        NaN                NaN   \n", "160     21       2     -96.10     307.62             -59.84   \n", "161     21       2     111.42     173.06              84.93   \n", "162     21       2    -105.80     119.78              -4.54   \n", "163     21       3     115.18     332.16              75.39   \n", "164     22       1     -51.21     323.75               4.82   \n", "165     22       2      80.46     170.29              71.25   \n", "166     22       2     -54.28     114.58              17.46   \n", "167     22       2     -61.56     270.92              37.67   \n", "168     22       2    -110.67     113.12              36.05   \n", "169     22       2      46.28     254.04              93.90   \n", "170     22       2      12.58     171.49             -37.74   \n", "171     22       2      54.61      66.70              39.68   \n", "172     22       2     -61.17     284.99             -27.69   \n", "173     22       2      75.01      87.45              54.77   \n", "174     22       2     -79.65     277.16             -42.31   \n", "175     22       2     -64.76       3.33              59.69   \n", "176     22       2      95.60      75.40              38.46   \n", "177     22       2     -54.03     309.12             -69.41   \n", "178     22       2    -106.27      99.77              38.62   \n", "179     22       2     100.10      67.96              89.15   \n", "180     22       2     -82.54     312.99             -82.13   \n", "181     22       2    -106.41      96.26              49.62   \n", "182     22       2      93.87     126.03              80.64   \n", "183     22       2      47.40     248.61             -60.23   \n", "184     22       2     -78.06     285.27              -7.21   \n", "185     22       2      21.95      79.36              63.94   \n", "186     22       2     -12.02      47.46             -42.67   \n", "187     22       2      79.71      76.98              28.16   \n", "188     22       2      71.55     313.20             -50.13   \n", "189     22       2     -95.55     285.49               1.48   \n", "190     22       2     -85.97     301.89              86.75   \n", "191     22       2      71.58     251.12              51.57   \n", "192     22       2     -79.24     282.57             -60.78   \n", "193     22       3     -93.05     -13.88              72.32   \n", "194     23       1        NaN        NaN                NaN   \n", "195     23       2    -104.80       7.67             -51.69   \n", "196     23       2     -76.91     304.51              69.79   \n", "197     23       2      91.69     271.00              58.36   \n", "198     23       2    -101.39     185.25             -39.76   \n", "199     23       2      91.84     230.27              69.83   \n", "200     23       2     -69.53     266.26             -28.22   \n", "201     23       2     -75.04      89.08              54.98   \n", "202     23       2     -62.95     318.29              42.25   \n", "203     23       2     -78.53     280.19              45.69   \n", "204     23       2      97.24     301.60              41.48   \n", "205     23       2     -97.31     173.52             -56.14   \n", "206     23       2      95.28      79.68              37.79   \n", "207     23       2     -93.04      39.47             -81.46   \n", "208     23       2      40.08     288.78              77.69   \n", "209     23       2    -104.21     132.75             -27.66   \n", "210     23       2     -68.83     298.83              68.89   \n", "211     23       2      93.85     145.19              46.17   \n", "212     23       2     -89.66     149.35               0.82   \n", "213     23       2      80.28     173.35              67.59   \n", "214     23       2     -36.18     148.58              -3.07   \n", "215     23       3      75.99     261.85              51.80   \n", "216     24       1     -78.98     294.29              18.64   \n", "217     24       2     -68.50     117.02              46.14   \n", "218     24       2     -82.76      13.14              62.35   \n", "219     24       2      98.02      77.50              58.44   \n", "220     24       2      68.22     121.20             -65.26   \n", "221     24       3      87.56     263.72              17.02   \n", "222     25       1        NaN        NaN                NaN   \n", "223     25       2     -51.31     185.18              34.72   \n", "224     25       2      53.81     207.55              38.23   \n", "225     25       2      56.83     103.00              -5.90   \n", "226     25       2      68.64     288.31             -29.53   \n", "227     25       2     -79.14     125.99             -42.87   \n", "228     25       2     -91.43     280.06              59.02   \n", "229     25       2      80.36     188.44              66.52   \n", "230     25       2    -105.72      82.37             -33.49   \n", "231     25       3     108.46     -97.61              83.92   \n", "232     26       1        NaN        NaN                NaN   \n", "233     26       2      30.13     260.89             -57.07   \n", "234     26       2    -103.99     307.75             -32.18   \n", "235     26       3     114.39     -42.62             100.90   \n", "236     27       1     -35.40     112.08               5.79   \n", "237     27       2      78.24     298.13              24.73   \n", "238     27       2     -82.88     222.24             -30.45   \n", "239     27       2     -82.89     104.48              74.31   \n", "240     27       2      79.46      97.24              53.07   \n", "241     27       2    -104.58      26.62             -56.74   \n", "242     27       3    -134.22     280.76              85.49   \n", "243     28       1        NaN        NaN                NaN   \n", "244     28       2     -87.80     285.43             -67.48   \n", "245     28       3     101.34     317.79              73.31   \n", "246     29       1      40.27      86.88              -9.10   \n", "247     29       2      37.23     234.93             -49.91   \n", "248     29       3    -111.13     232.90             -18.84   \n", "249     30       1        NaN        NaN                NaN   \n", "250     30       2     -91.64     284.17              85.31   \n", "251     30       2      85.00     137.58              60.30   \n", "252     30       2      81.29     227.55             -51.24   \n", "253     30       2    -111.39     299.54             -18.51   \n", "254     30       2      92.39     255.01              86.89   \n", "255     30       2    -102.56     293.88             -59.08   \n", "256     30       2     -93.62     156.50              93.84   \n", "257     30       2     109.28     232.01              29.29   \n", "258     30       2      73.47      66.49             -87.41   \n", "259     30       2    -113.04     305.12             -45.52   \n", "260     30       2      67.41     106.25              91.70   \n", "261     30       2      90.95      94.89             -29.24   \n", "262     30       2    -111.81      86.03             -58.17   \n", "263     30       2      82.10     299.08              83.58   \n", "264     30       2      75.22     112.86             -55.73   \n", "265     30       2     -93.89      97.41              18.61   \n", "266     30       2     100.51     273.55              69.53   \n", "267     30       2      39.20     277.92             -53.11   \n", "268     30       2      92.39     137.21              -6.82   \n", "269     30       2     -88.87      90.79               7.83   \n", "270     30       2     103.00       1.30              70.98   \n", "271     30       2     -27.06     303.68             -77.27   \n", "272     30       3    -109.86     -25.60              12.64   \n", "273     31       3      46.62      86.80              -8.09   \n", "274     32       1     -74.87     289.17              17.36   \n", "275     32       2    -110.51     102.03              44.04   \n", "276     32       2     104.42      66.14              87.60   \n", "277     32       2     -65.39     312.70             -86.26   \n", "278     32       3       0.72      -3.28              47.97   \n", "279     33       1     -38.13     103.24               2.82   \n", "280     33       2    -110.79     273.94              50.04   \n", "281     33       2     -75.42      88.44              64.87   \n", "282     33       2      88.48      60.98              56.58   \n", "283     33       2     -94.84     321.66             -67.14   \n", "284     33       2    -102.22     263.31              75.91   \n", "285     33       3     131.56     218.92              67.18   \n", "286     34       1      75.65     304.42              -4.11   \n", "287     34       2     -97.71     313.25             -80.50   \n", "288     34       3     100.64     310.43              81.06   \n", "289     35       1     -67.63     291.66              17.97   \n", "290     35       2      10.39     210.72              54.52   \n", "291     35       2     -84.09      92.61              -2.88   \n", "292     35       2     -99.69     313.28              50.07   \n", "293     35       2      88.56     165.60              66.96   \n", "294     35       2    -103.21     293.89             -68.59   \n", "295     35       2      87.17     284.05              71.48   \n", "296     35       2      95.15     170.27             -71.74   \n", "297     35       2     -98.14      26.54             -61.09   \n", "298     35       2      25.45     311.17              75.91   \n", "299     35       2      62.48     259.29             -14.98   \n", "300     35       2     -75.26     296.18             -47.54   \n", "301     35       2      89.83     168.67              71.23   \n", "302     35       2     -45.15     169.95              -6.84   \n", "303     35       2      -0.34     149.29              10.20   \n", "304     35       2      -8.64     159.91              -2.56   \n", "305     35       2      54.58     172.07              16.47   \n", "306     35       2     -29.84     273.65              12.67   \n", "307     35       2      57.79     129.48              41.08   \n", "308     35       2     -53.75     153.50              11.05   \n", "309     35       3      11.87     316.01              40.76   \n", "310     36       1        NaN        NaN                NaN   \n", "311     36       2     -92.67     149.39             -20.31   \n", "312     36       2      97.30     280.09              80.68   \n", "313     36       2     -96.02     318.85             -74.48   \n", "314     36       3      93.93     334.69              63.48   \n", "\n", "     player_location_y   type  opponent_location_x  opponent_location_y  \n", "0              -193.77    發長球               -43.66               213.01  \n", "1              -305.43     切球                 3.58               228.54  \n", "2              -149.55     挑球               -26.08               196.84  \n", "3              -282.76     長球                18.34               198.15  \n", "4              -283.08     殺球               -26.35               207.08  \n", "5              -190.96    擋小球                65.61               258.77  \n", "6               -99.20     勾球                39.04                99.80  \n", "7               -28.18     推球               -80.84               128.49  \n", "8                  NaN   未知球種                  NaN                  NaN  \n", "9              -210.05     長球               -40.47               194.02  \n", "10             -242.81     長球                19.66               205.58  \n", "11             -316.04     點扣               -31.19               181.99  \n", "12             -213.32    發長球                55.40               210.68  \n", "13             -298.46     長球                -9.01               233.87  \n", "14             -290.66     殺球                 7.86               183.13  \n", "15             -190.70    擋小球               -85.72               282.67  \n", "16             -123.50     推球                12.62               189.07  \n", "17             -267.89     殺球               -21.84               201.24  \n", "18             -205.96    擋小球               -21.51               269.28  \n", "19             -105.85     推球                17.28               215.64  \n", "20             -276.26     長球               -21.01               120.48  \n", "21             -213.92     殺球               -34.34               239.43  \n", "22             -228.65     勾球                 9.80               244.83  \n", "23             -123.45     挑球                -7.62               203.50  \n", "24             -301.63     長球                 9.92               164.82  \n", "25             -251.26     切球               -32.63               249.81  \n", "26              -77.59    放小球                27.69               179.92  \n", "27              -64.80     挑球               -37.90               118.84  \n", "28             -301.22  後場抽平球                19.49               144.34  \n", "29             -150.20     切球               -29.11               229.05  \n", "30             -150.90    發短球               -47.85               201.63  \n", "31             -123.54     推球                -1.82               147.73  \n", "32             -219.67     長球               -45.60               186.85  \n", "33             -282.30     切球                32.49               181.39  \n", "34             -150.81    發短球                41.58               215.26  \n", "35             -104.38     勾球                 0.65               150.86  \n", "36              -75.14    放小球                31.46               140.65  \n", "37                 NaN   未知球種                  NaN                  NaN  \n", "38             -201.35    放小球                 9.75               208.22  \n", "39              -98.31     挑球               -29.47               149.58  \n", "40             -271.55     殺球               -12.49               191.24  \n", "41             -206.97   防守回抽                20.02               267.62  \n", "42             -208.11     切球               -46.94               214.11  \n", "43             -103.58     挑球                18.99               180.01  \n", "44             -264.09     長球               -10.03               191.20  \n", "45             -267.18     殺球               -36.09               214.38  \n", "46             -205.82     勾球                54.20               240.62  \n", "47             -132.84     推球                -4.23               169.57  \n", "48             -211.01     長球               -61.22               177.39  \n", "49             -271.77     切球                 9.15               205.30  \n", "50             -167.55     挑球               -31.17               239.38  \n", "51             -276.94     切球                21.92               147.49  \n", "52             -126.07     挑球               -31.52               189.08  \n", "53             -285.16     切球                 0.15               150.87  \n", "54             -134.12     挑球                -9.56               218.57  \n", "55                 NaN   未知球種                  NaN                  NaN  \n", "56             -156.50    放小球               -40.34               281.93  \n", "57             -123.81     推球                25.91               145.87  \n", "58             -236.31     殺球               -54.50               177.29  \n", "59             -189.61     勾球                44.64               248.63  \n", "60                 NaN   未知球種                  NaN                  NaN  \n", "61             -236.18     殺球               -55.74               196.81  \n", "62             -182.50    擋小球                63.66               253.90  \n", "63             -106.22    放小球               -44.37               186.83  \n", "64                 NaN   未知球種                  NaN                  NaN  \n", "65             -272.49     長球               -29.15               193.95  \n", "66             -285.32     長球                17.52               181.53  \n", "67             -188.75     長球               -43.77               236.99  \n", "68             -293.66     殺球                 4.43               200.97  \n", "69                 NaN   未知球種                  NaN                  NaN  \n", "70             -263.63     切球                25.70               182.96  \n", "71             -135.84     推球               -39.97               236.93  \n", "72             -304.46     長球                16.27               164.76  \n", "73             -230.69     切球               -39.00               221.56  \n", "74             -153.93    發短球                35.62               197.89  \n", "75             -110.81     推球                -8.82               136.63  \n", "76             -291.75   過度切球                17.23               210.61  \n", "77             -196.23    發長球               -39.21               220.12  \n", "78             -290.35     殺球                 1.64               174.05  \n", "79             -218.21     勾球               -68.26               228.86  \n", "80             -126.41     挑球                -5.72               191.14  \n", "81             -296.16     長球                -5.69               169.59  \n", "82             -284.02   過度切球               -14.08               236.52  \n", "83             -101.76    放小球               -36.29               171.41  \n", "84              -71.66     挑球                30.28               108.95  \n", "85             -248.73     長球               -45.04               125.64  \n", "86             -267.50     殺球               -16.46               221.21  \n", "87             -150.09    放小球                27.38               240.55  \n", "88              -92.97     推球               -15.71               141.32  \n", "89             -267.57     點扣                 1.64               144.52  \n", "90             -146.21    發短球               -45.86               196.66  \n", "91             -125.50     推球                -3.83               138.18  \n", "92             -256.87     長球               -26.40               216.31  \n", "93             -288.24     切球                21.55               200.81  \n", "94             -143.56    放小球               -24.01               223.87  \n", "95              -95.15     勾球               -39.02               143.33  \n", "96             -108.12     挑球                16.35               183.49  \n", "97             -307.28  後場抽平球                 4.12               144.49  \n", "98             -176.98    擋小球               -49.93               301.29  \n", "99             -119.27     推球                 6.48               169.47  \n", "100            -286.44     切球               -19.90               193.81  \n", "101            -138.01     挑球               -31.78               177.46  \n", "102            -288.26     長球                -8.25               206.00  \n", "103            -275.16     殺球                -9.21               222.70  \n", "104            -144.40    擋小球                43.01               248.61  \n", "105             -99.60     挑球                77.30               121.11  \n", "106            -155.50    發短球                38.70               197.85  \n", "107             -91.07     撲球               -18.70               178.85  \n", "108            -175.76   防守回抽                18.51               157.15  \n", "109            -131.77    發短球               -46.65               226.77  \n", "110            -123.41     推球                -8.82               136.63  \n", "111            -283.92  後場抽平球               -13.94               218.64  \n", "112            -163.88    擋小球               -47.95               224.45  \n", "113            -137.70     挑球                 7.83               195.85  \n", "114            -321.61     長球                 3.06               174.09  \n", "115                NaN   未知球種                  NaN                  NaN  \n", "116            -298.75  後場抽平球               -12.97               171.19  \n", "117            -170.94     切球               -44.28               257.89  \n", "118            -137.52     挑球                 9.83               177.07  \n", "119                NaN   未知球種                  NaN                  NaN  \n", "120            -287.91     切球                14.61               183.06  \n", "121            -128.09     挑球               -30.10               213.85  \n", "122            -277.84     長球               -22.43               157.37  \n", "123            -280.14     長球                 8.05               243.95  \n", "124            -296.16     長球                -8.40               202.55  \n", "125            -224.86     長球               -46.00               250.03  \n", "126            -307.88     長球                21.36               183.00  \n", "127            -188.86     長球               -59.94               229.53  \n", "128            -307.47   過度切球                14.17               178.54  \n", "129             -87.49     推球               -59.25               266.10  \n", "130            -263.65     殺球                18.77               123.35  \n", "131            -160.37    擋小球               -68.09               245.15  \n", "132            -119.51     推球               -79.50               211.89  \n", "133            -158.62    發短球                36.80               195.41  \n", "134            -111.05    放小球               -11.67               152.56  \n", "135             -85.50     挑球                29.42               126.93  \n", "136            -281.55     殺球                 0.15               174.12  \n", "137            -208.49    擋小球               -18.72               198.72  \n", "138            -208.32    發長球                55.22               226.37  \n", "139            -297.12     長球               -17.69               218.70  \n", "140            -324.47     長球                 5.99               171.01  \n", "141            -266.84     切球               -25.59               247.09  \n", "142            -121.69    放小球               -28.94               196.86  \n", "143             -89.17     挑球                36.33               173.54  \n", "144            -291.05     殺球               -36.21               155.94  \n", "145            -171.49     勾球                50.74               275.03  \n", "146            -146.05     推球                -6.35               131.76  \n", "147            -245.99     長球               -39.63               194.10  \n", "148            -274.83     長球                24.30               206.62  \n", "149            -226.44     切球               -50.47               206.65  \n", "150             -88.82     挑球                18.93               184.52  \n", "151            -271.14     長球               -43.76               186.82  \n", "152            -233.27     點扣                11.45               213.95  \n", "153            -194.16    擋小球                34.55               238.34  \n", "154            -201.14    發長球               -40.86               227.21  \n", "155            -307.89     切球               -10.66               193.67  \n", "156             -91.00    放小球               -27.81               181.96  \n", "157             -73.30    放小球                33.78                80.50  \n", "158             -61.20     撲球               -49.60                80.15  \n", "159                NaN   未知球種                  NaN                  NaN  \n", "160            -276.49     長球               -26.29               208.77  \n", "161            -293.59     殺球                16.45               190.52  \n", "162            -167.95    擋小球               -55.92               221.82  \n", "163            -126.14     推球                23.31               181.48  \n", "164            -138.19    發長球               -47.78               177.19  \n", "165            -260.97     殺球                -1.79               175.66  \n", "166            -199.70    擋小球               -84.17               277.23  \n", "167            -134.73     挑球                 5.04               164.86  \n", "168            -249.31     切球               -16.91               203.65  \n", "169            -128.67     挑球                 6.35               196.54  \n", "170            -264.83     殺球               -38.68               179.46  \n", "171            -196.57    擋小球                31.88               263.55  \n", "172            -104.99     挑球               -16.41               216.15  \n", "173            -309.72     切球                 8.05               150.79  \n", "174            -144.11     挑球               -39.34               265.78  \n", "175            -279.54     切球                 3.09               163.34  \n", "176            -109.04    放小球               -25.96               229.00  \n", "177             -93.00     挑球               -22.82               163.59  \n", "178            -299.02     切球                10.12               166.75  \n", "179            -128.60    放小球                 3.54               175.61  \n", "180             -52.06     推球               -31.50               123.26  \n", "181            -265.94   過度切球                17.20               128.27  \n", "182            -126.01    放小球               -15.93               231.41  \n", "183             -80.15     推球               -35.23               155.93  \n", "184            -213.99     長球                11.84               152.55  \n", "185            -271.52     切球                22.33               210.98  \n", "186             -80.88    放小球               -41.45               219.07  \n", "187            -100.90    放小球                20.31               121.69  \n", "188             -59.62     推球               -27.07               141.48  \n", "189            -265.16     長球               -13.39               126.95  \n", "190            -284.13     長球                 5.93               181.22  \n", "191            -244.89     長球               -13.03               214.17  \n", "192            -247.13     長球               -39.46               242.10  \n", "193            -304.44     切球                23.91               202.25  \n", "194                NaN   未知球種                  NaN                  NaN  \n", "195            -119.84    放小球                -8.22               152.53  \n", "196             -80.37     推球                28.03               149.98  \n", "197            -298.66     長球                -0.34               147.71  \n", "198            -243.20     殺球               -38.08               236.90  \n", "199            -209.44   防守回抽                50.53               262.06  \n", "200            -200.75     長球               -56.57               184.58  \n", "201            -232.02     點扣                16.11               178.52  \n", "202            -151.29     推球               -48.54               226.80  \n", "203            -263.22     長球               -12.66               152.57  \n", "204            -265.47     長球               -20.87               223.82  \n", "205            -283.06     殺球               -27.30               183.46  \n", "206            -169.89    擋小球                51.57               259.00  \n", "207             -90.66    放小球               -32.91               128.78  \n", "208             -68.17     挑球                49.52               106.46  \n", "209            -283.53     殺球               -28.91               152.73  \n", "210            -170.19   防守回挑                10.08               267.64  \n", "211            -282.03  後場抽平球               -13.94               171.20  \n", "212            -177.17   過度切球               -36.29               273.76  \n", "213            -123.76     撲球                14.32               164.77  \n", "214            -169.50     平球               -65.83               170.25  \n", "215            -148.80     殺球               -25.38               232.68  \n", "216            -191.33    發長球               -51.93               230.12  \n", "217            -321.30     切球                -2.06               215.93  \n", "218            -125.95     勾球               -14.84               221.33  \n", "219             -66.18    放小球                 7.59               143.30  \n", "220             -48.53     推球               -49.61                64.31  \n", "221            -117.13     殺球                42.92               122.21  \n", "222                NaN   未知球種                  NaN                  NaN  \n", "223            -107.62     推球                -7.26               147.78  \n", "224            -221.53     平球               -16.93               143.65  \n", "225            -171.59    擋小球               -31.94               190.97  \n", "226            -161.53     挑球                 1.07               226.03  \n", "227            -296.97     殺球               -26.24               163.62  \n", "228            -162.39     挑球                35.05               297.10  \n", "229            -271.56  後場抽平球                 4.46               190.64  \n", "230            -179.87     切球               -30.33               191.51  \n", "231            -130.81    放小球                22.06               169.32  \n", "232                NaN   未知球種                  NaN                  NaN  \n", "233            -122.02     挑球               -10.35               131.80  \n", "234            -251.45     長球                -1.42               195.99  \n", "235            -299.37     殺球                27.91               215.24  \n", "236            -146.18    發短球               -41.97               189.24  \n", "237            -105.27     推球                -7.73               152.52  \n", "238            -247.39     殺球               -28.90               143.82  \n", "239            -227.20     勾球                36.23               237.71  \n", "240            -138.68    放小球               -18.08               196.24  \n", "241             -95.36    放小球               -58.76               109.26  \n", "242             -41.22     推球                29.53               100.11  \n", "243                NaN   未知球種                  NaN                  NaN  \n", "244            -276.43     長球               -29.82               234.19  \n", "245            -312.84     長球                24.28               208.07  \n", "246            -141.26    發短球                35.93               183.20  \n", "247             -99.84     推球               -19.42               162.01  \n", "248            -223.87     殺球                -6.87               171.78  \n", "249                NaN   未知球種                  NaN                  NaN  \n", "250            -315.85     長球                -0.34               150.87  \n", "251            -287.77     切球               -10.87               228.77  \n", "252            -124.36     推球               -30.58               228.53  \n", "253            -247.49     長球                 1.62               150.36  \n", "254            -279.98     長球                15.11               181.55  \n", "255            -253.94     長球               -32.16               224.00  \n", "256            -302.02     殺球                10.14               200.91  \n", "257            -202.74    擋小球               -78.30               261.09  \n", "258            -209.53     勾球              -106.02               167.48  \n", "259            -121.43     挑球                 0.44               223.49  \n", "260            -285.49     切球                10.35               172.49  \n", "261            -152.16     勾球               -57.63               255.47  \n", "262            -119.75    放小球                -5.70               166.51  \n", "263             -92.60     挑球                41.81               173.45  \n", "264            -269.58     殺球               -35.89               186.54  \n", "265            -184.88    擋小球                53.01               243.23  \n", "266            -142.09     推球                16.72               167.83  \n", "267            -220.73     長球               -53.97               179.69  \n", "268            -254.44     殺球                 7.27               202.40  \n", "269            -135.00    擋小球                14.02               278.33  \n", "270             -79.76    放小球                45.17               116.49  \n", "271             -53.92     挑球               -36.48               107.65  \n", "272            -303.44     切球                16.74               125.01  \n", "273            -142.86   未知球種                37.63               205.30  \n", "274            -186.43    發長球               -45.67               223.02  \n", "275            -282.50     切球               -12.51               193.69  \n", "276            -110.57    放小球                 4.97               181.65  \n", "277             -53.82     挑球               -55.49               170.10  \n", "278            -301.25     切球                21.22               128.23  \n", "279            -142.97    發短球               -54.66               201.74  \n", "280            -130.33     推球                -1.33               142.96  \n", "281            -285.24   過度切球                -5.83               223.59  \n", "282             -79.57    放小球                 9.49               157.05  \n", "283             -55.84     挑球               -46.10               116.70  \n", "284            -296.19     長球                19.19               129.87  \n", "285            -231.65     殺球               -26.48               221.37  \n", "286            -186.11    發長球                53.22               217.88  \n", "287            -290.51     長球               -37.97               231.75  \n", "288            -279.89     長球                12.43               157.02  \n", "289            -186.44    發長球               -50.01               231.50  \n", "290            -297.89     殺球                -1.43               208.39  \n", "291            -186.12   防守回抽               -38.34               201.36  \n", "292            -152.94     推球                 9.72               203.24  \n", "293            -266.22  後場抽平球                17.27               163.20  \n", "294            -164.25     推球               -55.21               219.28  \n", "295            -234.85     長球                20.60               136.33  \n", "296            -264.55     殺球               -50.72               237.10  \n", "297            -185.25    放小球                34.13               229.37  \n", "298             -82.17     挑球                18.66               171.40  \n", "299            -316.97     長球               -15.14               151.02  \n", "300            -232.05     長球               -22.19               228.94  \n", "301            -293.36     殺球                23.56               194.91  \n", "302            -195.06    擋小球               -81.39               258.49  \n", "303            -118.45     撲球                -0.80               198.08  \n", "304            -181.67   防守回抽               -22.38               148.38  \n", "305            -157.66     殺球                 0.64               163.36  \n", "306            -202.58   防守回挑               -12.35               171.86  \n", "307            -257.84     殺球               -13.94               171.20  \n", "308            -168.10    擋小球               -46.00               250.03  \n", "309            -139.37     撲球                -7.55               184.77  \n", "310                NaN   未知球種                  NaN                  NaN  \n", "311            -278.11     切球               -26.83               203.80  \n", "312             -77.75     挑球                15.28               208.15  \n", "313            -256.51     長球               -43.76               211.55  \n", "314            -276.90     長球                42.16               196.21  \n"]}], "source": ["import pandas as pd\n", "\n", "Data = pd.read_csv('Data\\match\\set1.csv')\n", "Data.to_excel('Data\\match\\set1.xlsx')\n", "pd.set_option('display.max_rows', len(Data))\n", "\n", "print(Data[['rally','server','landing_x','landing_y','player_location_x','player_location_y', 'type','opponent_location_x','opponent_location_y']])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     server  landing_x  landing_y  player_location_x  player_location_y\n", "164       1     -51.21     323.75               4.82            -138.19\n", "165       2      80.46     170.29              71.25            -260.97\n", "166       2     -54.28     114.58              17.46            -199.70\n", "167       2     -61.56     270.92              37.67            -134.73\n", "168       2    -110.67     113.12              36.05            -249.31\n", "169       2      46.28     254.04              93.90            -128.67\n", "170       2      12.58     171.49             -37.74            -264.83\n", "171       2      54.61      66.70              39.68            -196.57\n", "172       2     -61.17     284.99             -27.69            -104.99\n", "173       2      75.01      87.45              54.77            -309.72\n", "174       2     -79.65     277.16             -42.31            -144.11\n", "175       2     -64.76       3.33              59.69            -279.54\n", "176       2      95.60      75.40              38.46            -109.04\n", "177       2     -54.03     309.12             -69.41             -93.00\n", "178       2    -106.27      99.77              38.62            -299.02\n", "179       2     100.10      67.96              89.15            -128.60\n", "180       2     -82.54     312.99             -82.13             -52.06\n", "181       2    -106.41      96.26              49.62            -265.94\n", "182       2      93.87     126.03              80.64            -126.01\n", "183       2      47.40     248.61             -60.23             -80.15\n", "184       2     -78.06     285.27              -7.21            -213.99\n", "185       2      21.95      79.36              63.94            -271.52\n", "186       2     -12.02      47.46             -42.67             -80.88\n", "187       2      79.71      76.98              28.16            -100.90\n", "188       2      71.55     313.20             -50.13             -59.62\n", "189       2     -95.55     285.49               1.48            -265.16\n", "190       2     -85.97     301.89              86.75            -284.13\n", "191       2      71.58     251.12              51.57            -244.89\n", "192       2     -79.24     282.57             -60.78            -247.13\n", "193       3     -93.05     -13.88              72.32            -304.44\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "249       1        NaN        NaN                NaN                NaN\n", "250       2     -91.64     284.17              85.31            -315.85\n", "251       2      85.00     137.58              60.30            -287.77\n", "252       2      81.29     227.55             -51.24            -124.36\n", "253       2    -111.39     299.54             -18.51            -247.49\n", "254       2      92.39     255.01              86.89            -279.98\n", "255       2    -102.56     293.88             -59.08            -253.94\n", "256       2     -93.62     156.50              93.84            -302.02\n", "257       2     109.28     232.01              29.29            -202.74\n", "258       2      73.47      66.49             -87.41            -209.53\n", "259       2    -113.04     305.12             -45.52            -121.43\n", "260       2      67.41     106.25              91.70            -285.49\n", "261       2      90.95      94.89             -29.24            -152.16\n", "262       2    -111.81      86.03             -58.17            -119.75\n", "263       2      82.10     299.08              83.58             -92.60\n", "264       2      75.22     112.86             -55.73            -269.58\n", "265       2     -93.89      97.41              18.61            -184.88\n", "266       2     100.51     273.55              69.53            -142.09\n", "267       2      39.20     277.92             -53.11            -220.73\n", "268       2      92.39     137.21              -6.82            -254.44\n", "269       2     -88.87      90.79               7.83            -135.00\n", "270       2     103.00       1.30              70.98             -79.76\n", "271       2     -27.06     303.68             -77.27             -53.92\n", "272       3    -109.86     -25.60              12.64            -303.44\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "194       1        NaN        NaN                NaN                NaN\n", "195       2    -104.80       7.67             -51.69            -119.84\n", "196       2     -76.91     304.51              69.79             -80.37\n", "197       2      91.69     271.00              58.36            -298.66\n", "198       2    -101.39     185.25             -39.76            -243.20\n", "199       2      91.84     230.27              69.83            -209.44\n", "200       2     -69.53     266.26             -28.22            -200.75\n", "201       2     -75.04      89.08              54.98            -232.02\n", "202       2     -62.95     318.29              42.25            -151.29\n", "203       2     -78.53     280.19              45.69            -263.22\n", "204       2      97.24     301.60              41.48            -265.47\n", "205       2     -97.31     173.52             -56.14            -283.06\n", "206       2      95.28      79.68              37.79            -169.89\n", "207       2     -93.04      39.47             -81.46             -90.66\n", "208       2      40.08     288.78              77.69             -68.17\n", "209       2    -104.21     132.75             -27.66            -283.53\n", "210       2     -68.83     298.83              68.89            -170.19\n", "211       2      93.85     145.19              46.17            -282.03\n", "212       2     -89.66     149.35               0.82            -177.17\n", "213       2      80.28     173.35              67.59            -123.76\n", "214       2     -36.18     148.58              -3.07            -169.50\n", "215       3      75.99     261.85              51.80            -148.80\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "289       1     -67.63     291.66              17.97            -186.44\n", "290       2      10.39     210.72              54.52            -297.89\n", "291       2     -84.09      92.61              -2.88            -186.12\n", "292       2     -99.69     313.28              50.07            -152.94\n", "293       2      88.56     165.60              66.96            -266.22\n", "294       2    -103.21     293.89             -68.59            -164.25\n", "295       2      87.17     284.05              71.48            -234.85\n", "296       2      95.15     170.27             -71.74            -264.55\n", "297       2     -98.14      26.54             -61.09            -185.25\n", "298       2      25.45     311.17              75.91             -82.17\n", "299       2      62.48     259.29             -14.98            -316.97\n", "300       2     -75.26     296.18             -47.54            -232.05\n", "301       2      89.83     168.67              71.23            -293.36\n", "302       2     -45.15     169.95              -6.84            -195.06\n", "303       2      -0.34     149.29              10.20            -118.45\n", "304       2      -8.64     159.91              -2.56            -181.67\n", "305       2      54.58     172.07              16.47            -157.66\n", "306       2     -29.84     273.65              12.67            -202.58\n", "307       2      57.79     129.48              41.08            -257.84\n", "308       2     -53.75     153.50              11.05            -168.10\n", "309       3      11.87     316.01              40.76            -139.37\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "12       1      22.46     307.30              -4.17            -213.32\n", "13       2     -81.51     261.14             -35.86            -298.46\n", "14       2      87.86     183.88              73.73            -290.66\n", "15       2     -75.37     112.63               2.25            -190.70\n", "16       2     -37.90     264.12              48.65            -123.50\n", "17       2     106.80     201.76              12.96            -267.89\n", "18       2     -77.86      44.79               5.77            -205.96\n", "19       2     -35.41     329.18              53.81            -105.85\n", "20       2      27.00     258.24              24.72            -276.26\n", "21       2      80.22     175.29             -15.22            -213.92\n", "22       2      45.18      66.80               3.33            -228.65\n", "23       2    -101.52     310.51              -9.21            -123.45\n", "24       2     106.45     256.24              70.24            -301.63\n", "25       2     -77.90      79.85             -55.54            -251.26\n", "26       2      76.53      68.22              68.59             -77.59\n", "27       2     -97.21     341.75             -65.69             -64.80\n", "28       2     104.33     163.90              46.00            -301.22\n", "29       3    -106.16      80.23             -67.81            -150.20\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "37       1        NaN        NaN                NaN                NaN\n", "38       2       6.13      72.33              37.87            -201.35\n", "39       2      23.30     285.84              -0.99             -98.31\n", "40       2    -100.25     230.16             -23.57            -271.55\n", "41       2      71.39     203.28              71.60            -206.97\n", "42       2     -74.06      84.10             -20.02            -208.11\n", "43       2     -56.24     289.02              62.68            -103.58\n", "44       2      98.96     285.08              34.28            -264.09\n", "45       2     -78.78     216.19             -38.97            -267.18\n", "46       2    -101.83     151.86              47.68            -205.82\n", "47       2      90.44     263.07              66.18            -132.84\n", "48       2     -98.82     321.73             -19.03            -211.01\n", "49       2      77.44      80.50              79.41            -271.77\n", "50       2    -116.32     305.17             -46.47            -167.55\n", "51       2      92.16      60.93              66.07            -276.94\n", "52       2     -92.81     307.57             -72.51            -126.07\n", "53       2    -117.41      54.32              71.56            -285.16\n", "54       3     134.26     334.86              80.18            -134.12\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "138       1      46.75     302.14              -3.54            -208.32\n", "139       2     -96.32     299.29             -46.05            -297.12\n", "140       2     -66.03     280.09              93.00            -324.47\n", "141       2      98.91     121.43              47.30            -266.84\n", "142       2    -116.95      19.24             -80.06            -121.69\n", "143       2     105.42     295.93              83.78             -89.17\n", "144       2    -103.34      96.23             -68.09            -291.05\n", "145       2     -64.92     146.66              44.54            -171.49\n", "146       2      98.62     263.00              22.35            -146.05\n", "147       2    -115.75     327.70             -34.16            -245.99\n", "148       2      92.67     231.66             102.14            -274.83\n", "149       2    -120.33      84.72             -47.28            -226.44\n", "150       2      83.29     301.86             103.24             -88.82\n", "151       2      80.19     285.39             -74.08            -271.14\n", "152       2    -103.05     187.16             -28.79            -233.27\n", "153       3      93.75      -1.75              56.22            -194.16\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "90        1     -23.98      94.27               8.76            -146.21\n", "91        2      94.77     283.99              29.77            -125.50\n", "92        2     -92.77     293.72             -30.22            -256.87\n", "93        2    -106.19     119.71              93.14            -288.24\n", "94        2     109.82     134.93              61.31            -143.56\n", "95        2      79.15      48.46             -72.40             -95.15\n", "96        2     -88.06     290.90             -49.51            -108.12\n", "97        2      59.58     168.96              75.71            -307.28\n", "98        2     -99.42     151.82             -18.55            -176.98\n", "99        2     -74.74     276.26              69.18            -119.27\n", "100       2      71.03     117.32              53.25            -286.44\n", "101       2      68.05     290.61             -54.64            -138.01\n", "102       2      70.69     291.01             -41.49            -288.26\n", "103       2     114.15     127.28             -43.01            -275.16\n", "104       2    -120.49      86.88             -14.03            -144.40\n", "105       3      58.67     337.30              93.43             -99.60\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "119       1        NaN        NaN                NaN                NaN\n", "120       2      56.75      78.98              72.97            -287.91\n", "121       2      70.25     296.52             -34.76            -128.09\n", "122       2     -82.98     273.71             -43.72            -277.84\n", "123       2     -84.66     301.87              72.28            -280.14\n", "124       2     106.20     242.64              73.95            -296.16\n", "125       2    -114.56     319.16             -66.97            -224.86\n", "126       2      66.29     247.09             111.23            -307.88\n", "127       2    -118.83     313.60             -35.19            -188.86\n", "128       2      89.34      46.53              86.89            -307.47\n", "129       2     -96.54     291.04             -71.39             -87.49\n", "130       2     -74.48     165.63              72.64            -263.65\n", "131       2      98.93      92.60              10.63            -160.37\n", "132       3      86.81     332.36             -75.25            -119.51\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "77       1     -82.11     306.85              17.46            -196.23\n", "78       2      97.47     211.89              62.83            -290.35\n", "79       2      68.80     119.55             -14.18            -218.21\n", "80       2     -83.32     315.82             -39.83            -126.41\n", "81       2    -108.43     290.70              73.95            -296.16\n", "82       2      89.93     108.12              72.50            -284.02\n", "83       2    -105.30      54.18             -69.78            -101.76\n", "84       2      70.25     296.52              74.37             -71.66\n", "85       2      71.77     310.65             -35.39            -248.73\n", "86       2      66.69     126.41             -35.40            -267.50\n", "87       2      63.28      84.13             -20.83            -150.09\n", "88       2      49.47     299.63             -40.77             -92.97\n", "89       3      24.64     -25.29             -14.58            -267.57\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "222       1        NaN        NaN                NaN                NaN\n", "223       2     -51.31     185.18              34.72            -107.62\n", "224       2      53.81     207.55              38.23            -221.53\n", "225       2      56.83     103.00              -5.90            -171.59\n", "226       2      68.64     288.31             -29.53            -161.53\n", "227       2     -79.14     125.99             -42.87            -296.97\n", "228       2     -91.43     280.06              59.02            -162.39\n", "229       2      80.36     188.44              66.52            -271.56\n", "230       2    -105.72      82.37             -33.49            -179.87\n", "231       3     108.46     -97.61              83.92            -130.81\n", "   server  landing_x  landing_y  player_location_x  player_location_y\n", "0       1     -80.77     300.60              17.44            -193.77\n", "1       2      92.61     103.66              58.57            -305.43\n", "2       2     -97.17     293.16             -58.02            -149.55\n", "3       2      83.93     296.29              76.59            -282.76\n", "4       2      50.11     119.74             -54.85            -283.08\n", "5       2     -85.51      82.10              30.02            -190.96\n", "6       2      83.80      41.10              64.26             -99.20\n", "7       3      13.66     316.99             -80.76             -28.18\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "279       1     -38.13     103.24               2.82            -142.97\n", "280       2    -110.79     273.94              50.04            -130.33\n", "281       2     -75.42      88.44              64.87            -285.24\n", "282       2      88.48      60.98              56.58             -79.57\n", "283       2     -94.84     321.66             -67.14             -55.84\n", "284       2    -102.22     263.31              75.91            -296.19\n", "285       3     131.56     218.92              67.18            -231.65\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "236       1     -35.40     112.08               5.79            -146.18\n", "237       2      78.24     298.13              24.73            -105.27\n", "238       2     -82.88     222.24             -30.45            -247.39\n", "239       2     -82.89     104.48              74.31            -227.20\n", "240       2      79.46      97.24              53.07            -138.68\n", "241       2    -104.58      26.62             -56.74             -95.36\n", "242       3    -134.22     280.76              85.49             -41.22\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "109       1     -28.98     114.23               7.35            -131.77\n", "110       2     -90.99     293.11              42.74            -123.41\n", "111       2      82.77     229.86              60.48            -283.92\n", "112       2      68.16     101.19             -43.39            -163.88\n", "113       2    -103.21     293.89             -44.00            -137.70\n", "114       3      80.32     339.51              92.19            -321.61\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "216       1     -78.98     294.29              18.64            -191.33\n", "217       2     -68.50     117.02              46.14            -321.30\n", "218       2     -82.76      13.14              62.35            -125.95\n", "219       2      98.02      77.50              58.44             -66.18\n", "220       2      68.22     121.20             -65.26             -48.53\n", "221       3      87.56     263.72              17.02            -117.13\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "55       1        NaN        NaN                NaN                NaN\n", "56       2     -97.06     101.85             -66.03            -156.50\n", "57       2      92.85     273.61              71.14            -123.81\n", "58       2     -88.79     240.29             -38.58            -236.31\n", "59       3    -113.19     132.84              66.58            -189.61\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "159       1        NaN        NaN                NaN                NaN\n", "160       2     -96.10     307.62             -59.84            -276.49\n", "161       2     111.42     173.06              84.93            -293.59\n", "162       2    -105.80     119.78              -4.54            -167.95\n", "163       3     115.18     332.16              75.39            -126.14\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "274       1     -74.87     289.17              17.36            -186.43\n", "275       2    -110.51     102.03              44.04            -282.50\n", "276       2     104.42      66.14              87.60            -110.57\n", "277       2     -65.39     312.70             -86.26             -53.82\n", "278       3       0.72      -3.28              47.97            -301.25\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "64       1        NaN        NaN                NaN                NaN\n", "65       2    -101.45     285.65             -69.94            -272.49\n", "66       2      93.44     207.45              81.30            -285.32\n", "67       2     -88.95     269.25             -46.71            -188.75\n", "68       3    -138.04     223.83              89.50            -293.66\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "310       1        NaN        NaN                NaN                NaN\n", "311       2     -92.67     149.39             -20.31            -278.11\n", "312       2      97.30     280.09              80.68             -77.75\n", "313       2     -96.02     318.85             -74.48            -256.51\n", "314       3      93.93     334.69              63.48            -276.90\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "154       1     -81.10     315.47              15.03            -201.14\n", "155       2      80.62      97.22              55.83            -307.89\n", "156       2     -85.70      37.54             -56.58             -91.00\n", "157       2      74.84     110.56              63.36             -73.30\n", "158       3     107.56     214.53             -49.86             -61.20\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "133       1      34.63      93.48              -8.49            -158.62\n", "134       2    -102.18      30.32             -39.11            -111.05\n", "135       2     -19.70     289.77              63.35             -85.50\n", "136       2     -38.09     210.08              17.02            -281.55\n", "137       3      13.87     -19.90              22.54            -208.49\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "69       1        NaN        NaN                NaN                NaN\n", "70       2      90.97      71.59              71.36            -263.63\n", "71       2    -107.12     305.02             -68.51            -135.84\n", "72       2      88.08     264.42              73.63            -304.46\n", "73       3    -107.60      75.97             -43.87            -230.69\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "60       1        NaN        NaN                NaN                NaN\n", "61       2    -108.29     207.53             -53.88            -236.18\n", "62       2      88.89      62.76              79.78            -182.50\n", "63       3    -103.28      -5.16             -70.47            -106.22\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "8        1        NaN        NaN                NaN                NaN\n", "9        2     -93.78     267.22             -53.89            -210.05\n", "10       2      94.00     341.33              76.27            -242.81\n", "11       3    -127.55     166.15             -69.68            -316.04\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "232       1        NaN        NaN                NaN                NaN\n", "233       2      30.13     260.89             -57.07            -122.02\n", "234       2    -103.99     307.75             -32.18            -251.45\n", "235       3     114.39     -42.62             100.90            -299.37\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "115       1        NaN        NaN                NaN                NaN\n", "116       2      72.75     168.83              63.59            -298.75\n", "117       2     -94.76     133.25             -13.28            -170.94\n", "118       3    -144.83     308.56              72.92            -137.52\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "30       1     -50.24      79.48               3.30            -150.90\n", "31       2      80.79     257.80              51.61            -123.54\n", "32       2    -100.70     307.70             -12.34            -219.67\n", "33       3     133.73     -44.92              63.02            -282.30\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "106       1      30.32     111.17              -8.02            -155.50\n", "107       2     -14.75     186.34             -51.38             -91.07\n", "108       3      42.60     313.69              12.93            -175.76\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "243       1        NaN        NaN                NaN                NaN\n", "244       2     -87.80     285.43             -67.48            -276.43\n", "245       3     101.34     317.79              73.31            -312.84\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "246       1      40.27      86.88              -9.10            -141.26\n", "247       2      37.23     234.93             -49.91             -99.84\n", "248       3    -111.13     232.90             -18.84            -223.87\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "34       1      28.25      93.56              -6.07            -150.81\n", "35       2     -83.20      62.90             -40.12            -104.38\n", "36       3      87.74     -11.29              71.08             -75.14\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "286       1      75.65     304.42              -4.11            -186.11\n", "287       2     -97.71     313.25             -80.50            -290.51\n", "288       3     100.64     310.43              81.06            -279.89\n", "    server  landing_x  landing_y  player_location_x  player_location_y\n", "74       1      48.48      91.11              -9.01            -153.93\n", "75       2    -113.88     284.34             -56.10            -110.81\n", "76       3     -44.60       3.81              79.58            -291.75\n", "     server  landing_x  landing_y  player_location_x  player_location_y\n", "273       3      46.62       86.8              -8.09            -142.86\n"]}], "source": ["rally = Data['rally'].value_counts().keys()\n", "for i in rally:\n", "    rally_data = Data[Data['rally']==i] \n", "    print(rally_data[['server','landing_x','landing_y','player_location_x','player_location_y']])"]}], "metadata": {"interpreter": {"hash": "df95319d8ce4e1d89f5365ae10992bc1f65da593082b1d264e8f529830ec2f02"}, "kernelspec": {"display_name": "Python 3.10.4 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}