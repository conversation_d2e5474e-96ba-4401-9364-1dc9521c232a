{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "ball_type_word_to_num = {\n", "\n", "                '挑球': 0, '推球': 1, '放小球': 2, '擋小球': 3, '平球': 4, '小平球': 5,\n", "                '撲球': 6, '防守回挑': 7,  '勾球': 8,  '後場抽平球': 9,\n", "                '點扣': 10, '長球': 11,  '殺球': 12, '防守回抽': 13,\n", "                '過度切球': 14, '切球': 15, '發短球': 16,  '發長球': 17,\n", "                '未知球種': 18\n", "            }\n", "table = pd.DataFrame({\n", "    'chinese':ball_type_word_to_num.keys()\n", "},index=ball_type_word_to_num.values())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "table['english'] = [np.nan]*19"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chinese</th>\n", "      <th>english</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>挑球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>推球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>放小球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>擋小球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>平球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>小平球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>撲球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>防守回挑</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>勾球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>後場抽平球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>點扣</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>長球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>殺球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>防守回抽</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>過度切球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>切球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>發短球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>發長球</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>未知球種</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   chinese  english\n", "0       挑球      NaN\n", "1       推球      NaN\n", "2      放小球      NaN\n", "3      擋小球      NaN\n", "4       平球      NaN\n", "5      小平球      NaN\n", "6       撲球      NaN\n", "7     防守回挑      NaN\n", "8       勾球      NaN\n", "9    後場抽平球      NaN\n", "10      點扣      NaN\n", "11      長球      NaN\n", "12      殺球      NaN\n", "13    防守回抽      NaN\n", "14    過度切球      NaN\n", "15      切球      NaN\n", "16     發短球      NaN\n", "17     發長球      NaN\n", "18    未知球種      NaN"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["table"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["table.loc[10,'english'] = 'net shot'\n", "table.loc[0,'english'] = 'lob'\n", "table.loc[2,'english'] = 'return net'\n", "table.loc[11,'english'] = 'clear'\n", "table.loc[1,'english'] = 'push'\n", "table.loc[16,'english'] = 'short service'\n", "table.loc[15,'english'] = 'drop'\n", "table.loc[12,'english'] = 'smash'\n", "table.loc[10,'english'] = 'wrist smash'\n", "table.loc[14,'english'] = 'passive drop'\n", "table.loc[8,'english'] = 'cross-court net shot'\n", "table.loc[5,'english'] = 'drive'\n", "table.loc[17,'english'] = 'long service'\n", "table.loc[9,'english'] = 'back-court drive'\n", "table.loc[13,'english'] = 'defensive return drive'\n", "table.loc[7,'english'] = 'defensive return lob'\n", "table.loc[6,'english'] = 'rush'\n", "table.loc[4,'english'] = 'driven flight'\n", "table.to_excel('Data/ball_type_table.xlsx')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["table.to_excel(r'Data\\ball_type_table.xlsx')"]}], "metadata": {"interpreter": {"hash": "df95319d8ce4e1d89f5365ae10992bc1f65da593082b1d264e8f529830ec2f02"}, "kernelspec": {"display_name": "Python 3.10.4 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}