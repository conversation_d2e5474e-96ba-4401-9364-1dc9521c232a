<!DOCTYPE html>
<html>
<head>
    <link rel="shortcut icon" href="image/badminton.ico"/>
    <link rel="bookmark" href="image/badminton.ico"/>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <title>CoachAI</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/css?family=PT+Sans+Narrow" rel="stylesheet">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <script src="https://d3js.org/d3.v4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.5.0"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lodash.js/2.4.1/lodash.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css">
    <script src="js/realdata.js"></script>
    <link rel="stylesheet"  href="css/text.css">
</head>
<body>
    <div class="navbar">
        <nav class="navbar navbar-inverse">
            <div class="container-fluid">
                <div class="navbar-header">
                    <a class="navbar-brand" href="./home.html">CoachAI</a>
                </div>
                <ul class="nav navbar-nav">
                <li><a href="./home.html">Home</a></li>
                <li class="active"><a href="./realdata.html">Real Data</a></li>
                <li><a href="./ourdata.html">Our Data</a></li>
                <li><a href="./trajectory.html">Trajectory</a></li>
                <li><a href="./autoanalysis.html">Auto Analysis</a></li>
                </ul>
            </div>
        </nav>
    </div>
    <div class="container">
        <div id="dropdown" class="row form-group form-inline">
            <label for="game">Game:</label>
            <select id="game" class="form-control" onchange=change_game()>
                <script type="text/javascript">get_interval_game();</script>
            </select>
            <label for="set">Set:</label>
            <select id="set" class="form-control" onchange=change_set()>
                <script type="text/javascript">get_interval_set();</script>
            </select>
            <label for="down">Down:</label>
            <select id="down" class="form-control">
                <script type="text/javascript">get_interval_updown();</script>
            </select>
            <label for="up">Up:</label>
            <select id="up" class="form-control"></select>
            <button id="interval-submit" type="button" class="btn btn-primary" onclick=change_interval()>查詢 <i class="fab fa-sistrix"></i></button>
        </div>
        <div id="on_off_court" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
            <script type="text/javascript">init_on_off_court();</script>
        </div>
        <div id="lose_error" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
            <script type="text/javascript">init_lose_error();</script>
        </div>
        <div id="total_balltype" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
        </div>
        <div id="sum_balltype" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
            <script type="text/javascript">init_total_balltype();</script>
        </div>
        <div id="stroke_distribution" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
            <script type="text/javascript">init_stroke_distribution();</script>
        </div>
        <div id="total_court" class="row">
            <div class="col-md-6 playerA"></div>
            <div class="col-md-6 playerB"></div>
            <script type="text/javascript">init_court_distribution();</script>
        </div>
        <div id="line" class="row">
            <script type="text/javascript">init_linechart();</script>
        </div>
        
        <div class="modal" id="radarChart" role="dialog" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button id="closechart" type="button" class="close" data-dismiss="modal">
                            <span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" id="rallytitle"></h4>
                    </div>
                    <div class="modal-body">
                        <canvas id="canvas" width="1000" height="800"></canvas>
                        <div class="modal-text" id="lose_reason"></div>
                        <div class="modal-text" id="lose_balltype"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>