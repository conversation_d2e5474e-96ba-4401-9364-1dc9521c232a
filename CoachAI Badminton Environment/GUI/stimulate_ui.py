# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'stimulate.ui'
##
## Created by: Qt User Interface Compiler version 6.4.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QGroupBox,
    QHeaderView, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QSlider, QTableWidget, QTableWidgetItem,
    QWidget)

class Ui_Form(object):
    def setupUi(self, Form):
        if not Form.objectName():
            Form.setObjectName(u"Form")
        Form.resize(818, 621)
        self.label = QLabel(Form)
        self.label.setObjectName(u"label")
        self.label.setGeometry(QRect(10, 340, 41, 16))
        self.playerB_y = QLineEdit(Form)
        self.playerB_y.setObjectName(u"playerB_y")
        self.playerB_y.setGeometry(QRect(200, 550, 41, 20))
        self.playerB_y.setReadOnly(True)
        self.landing_x = QLineEdit(Form)
        self.landing_x.setObjectName(u"landing_x")
        self.landing_x.setGeometry(QRect(150, 500, 41, 20))
        self.landing_x.setReadOnly(True)
        self.label_5 = QLabel(Form)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setGeometry(QRect(150, 530, 91, 20))
        self.label_5.setAlignment(Qt.AlignCenter)
        self.label_4 = QLabel(Form)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setGeometry(QRect(150, 430, 91, 20))
        self.label_4.setAlignment(Qt.AlignCenter)
        self.playerB_x = QLineEdit(Form)
        self.playerB_x.setObjectName(u"playerB_x")
        self.playerB_x.setGeometry(QRect(150, 550, 41, 20))
        self.playerB_x.setReadOnly(True)
        self.ball_round_id = QLineEdit(Form)
        self.ball_round_id.setObjectName(u"ball_round_id")
        self.ball_round_id.setGeometry(QRect(10, 370, 71, 20))
        self.ball_round_id.setReadOnly(True)
        self.auto_nextball = QCheckBox(Form)
        self.auto_nextball.setObjectName(u"auto_nextball")
        self.auto_nextball.setGeometry(QRect(190, 370, 51, 20))
        self.nextball = QPushButton(Form)
        self.nextball.setObjectName(u"nextball")
        self.nextball.setEnabled(False)
        self.nextball.setGeometry(QRect(100, 370, 71, 21))
        self.landing_y = QLineEdit(Form)
        self.landing_y.setObjectName(u"landing_y")
        self.landing_y.setGeometry(QRect(200, 500, 41, 20))
        self.landing_y.setReadOnly(True)
        self.ball_round_choose = QSlider(Form)
        self.ball_round_choose.setObjectName(u"ball_round_choose")
        self.ball_round_choose.setEnabled(False)
        self.ball_round_choose.setGeometry(QRect(60, 340, 171, 21))
        self.ball_round_choose.setOrientation(Qt.Horizontal)
        self.label_6 = QLabel(Form)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setGeometry(QRect(150, 480, 91, 20))
        self.label_6.setAlignment(Qt.AlignCenter)
        self.type_distribution = QLabel(Form)
        self.type_distribution.setObjectName(u"type_distribution")
        self.type_distribution.setGeometry(QRect(0, 10, 441, 311))
        self.type_distribution.setAutoFillBackground(False)
        self.type_distribution.setStyleSheet(u"")
        self.type_distribution.setScaledContents(True)
        self.type_distribution.setAlignment(Qt.AlignCenter)
        self.playerA_y = QLineEdit(Form)
        self.playerA_y.setObjectName(u"playerA_y")
        self.playerA_y.setGeometry(QRect(150, 450, 41, 20))
        self.playerA_y.setReadOnly(True)
        self.playerA_x = QLineEdit(Form)
        self.playerA_x.setObjectName(u"playerA_x")
        self.playerA_x.setGeometry(QRect(200, 450, 41, 20))
        self.playerA_x.setReadOnly(True)
        self.lose_state_table = QTableWidget(Form)
        if (self.lose_state_table.columnCount() < 4):
            self.lose_state_table.setColumnCount(4)
        __qtablewidgetitem = QTableWidgetItem()
        self.lose_state_table.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.lose_state_table.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.lose_state_table.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.lose_state_table.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        if (self.lose_state_table.rowCount() < 5):
            self.lose_state_table.setRowCount(5)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.lose_state_table.setVerticalHeaderItem(0, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.lose_state_table.setVerticalHeaderItem(1, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.lose_state_table.setVerticalHeaderItem(2, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.lose_state_table.setVerticalHeaderItem(3, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.lose_state_table.setVerticalHeaderItem(4, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        __qtablewidgetitem9.setTextAlignment(Qt.AlignCenter);
        self.lose_state_table.setItem(2, 0, __qtablewidgetitem9)
        self.lose_state_table.setObjectName(u"lose_state_table")
        self.lose_state_table.setGeometry(QRect(460, 130, 331, 171))
        font = QFont()
        font.setPointSize(8)
        self.lose_state_table.setFont(font)
        self.lose_state_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.lose_state_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.lose_state_table.horizontalHeader().setMinimumSectionSize(30)
        self.lose_state_table.horizontalHeader().setDefaultSectionSize(63)
        self.lose_state_table.verticalHeader().setMinimumSectionSize(30)
        self.lose_state_table.verticalHeader().setProperty("showSortIndicator", False)
        self.groupBox_2 = QGroupBox(Form)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.groupBox_2.setGeometry(QRect(460, 60, 341, 41))
        self.A_win_ratio = QLabel(self.groupBox_2)
        self.A_win_ratio.setObjectName(u"A_win_ratio")
        self.A_win_ratio.setGeometry(QRect(10, 0, 181, 21))
        font1 = QFont()
        font1.setPointSize(11)
        self.A_win_ratio.setFont(font1)
        self.B_win_ratio = QLabel(self.groupBox_2)
        self.B_win_ratio.setObjectName(u"B_win_ratio")
        self.B_win_ratio.setGeometry(QRect(10, 20, 181, 21))
        self.B_win_ratio.setFont(font1)
        self.save = QPushButton(self.groupBox_2)
        self.save.setObjectName(u"save")
        self.save.setGeometry(QRect(270, 10, 61, 21))
        self.lose_reason_pie = QLabel(Form)
        self.lose_reason_pie.setObjectName(u"lose_reason_pie")
        self.lose_reason_pie.setGeometry(QRect(0, 420, 151, 191))
        self.lose_reason_pie.setAutoFillBackground(False)
        self.lose_reason_pie.setStyleSheet(u"")
        self.lose_reason_pie.setScaledContents(True)
        self.lose_reason_pie.setAlignment(Qt.AlignCenter)
        self.history_landing_distribution = QGroupBox(Form)
        self.history_landing_distribution.setObjectName(u"history_landing_distribution")
        self.history_landing_distribution.setGeometry(QRect(250, 320, 551, 281))
        self.history_landing_distribution.setStyleSheet(u"font-weight:bold;")
        self.statistic_ball = QComboBox(self.history_landing_distribution)
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.addItem("")
        self.statistic_ball.setObjectName(u"statistic_ball")
        self.statistic_ball.setGeometry(QRect(10, 150, 91, 22))
        self.statistic_ball.setLayoutDirection(Qt.LeftToRight)
        self.statistic_ball.setStyleSheet(u"font-weight:normal")
        self.label_7 = QLabel(self.history_landing_distribution)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setGeometry(QRect(0, 80, 111, 20))
        font2 = QFont()
        font2.setPointSize(8)
        font2.setBold(False)
        self.label_7.setFont(font2)
        self.label_7.setStyleSheet(u"font-weight:normal")
        self.label_7.setAlignment(Qt.AlignCenter)
        self.statistic_player = QComboBox(self.history_landing_distribution)
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.addItem("")
        self.statistic_player.setObjectName(u"statistic_player")
        self.statistic_player.setGeometry(QRect(10, 50, 91, 22))
        self.statistic_player.setStyleSheet(u"font-weight:normal")
        self.statistic_opponent = QComboBox(self.history_landing_distribution)
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.addItem("")
        self.statistic_opponent.setObjectName(u"statistic_opponent")
        self.statistic_opponent.setGeometry(QRect(10, 100, 91, 22))
        self.statistic_opponent.setStyleSheet(u"font-weight:normal")
        self.label_2 = QLabel(self.history_landing_distribution)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setGeometry(QRect(0, 30, 111, 20))
        self.label_2.setFont(font2)
        self.label_2.setStyleSheet(u"font-weight:normal")
        self.label_2.setAlignment(Qt.AlignCenter)
        self.label_8 = QLabel(self.history_landing_distribution)
        self.label_8.setObjectName(u"label_8")
        self.label_8.setGeometry(QRect(0, 130, 111, 20))
        self.label_8.setFont(font2)
        self.label_8.setStyleSheet(u"font-weight:normal")
        self.label_8.setAlignment(Qt.AlignCenter)
        self.history_landing_image = QLabel(self.history_landing_distribution)
        self.history_landing_image.setObjectName(u"history_landing_image")
        self.history_landing_image.setGeometry(QRect(110, 50, 221, 231))
        self.history_landing_image.setStyleSheet(u"")
        self.history_landing_image.setScaledContents(False)
        self.history_landing_image.setAlignment(Qt.AlignCenter)
        self.history_landing_update = QPushButton(self.history_landing_distribution)
        self.history_landing_update.setObjectName(u"history_landing_update")
        self.history_landing_update.setGeometry(QRect(10, 250, 91, 24))
        self.history_landing_update.setStyleSheet(u"font-weight:normal")
        self.history_movement_image = QLabel(self.history_landing_distribution)
        self.history_movement_image.setObjectName(u"history_movement_image")
        self.history_movement_image.setGeometry(QRect(330, 50, 221, 231))
        self.history_movement_image.setStyleSheet(u"")
        self.history_movement_image.setScaledContents(False)
        self.history_movement_image.setAlignment(Qt.AlignCenter)
        self.label_10 = QLabel(self.history_landing_distribution)
        self.label_10.setObjectName(u"label_10")
        self.label_10.setGeometry(QRect(110, 10, 221, 21))
        font3 = QFont()
        font3.setPointSize(12)
        font3.setBold(True)
        self.label_10.setFont(font3)
        self.label_10.setAlignment(Qt.AlignCenter)
        self.label_11 = QLabel(self.history_landing_distribution)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setGeometry(QRect(330, 10, 221, 20))
        self.label_11.setFont(font3)
        self.label_11.setAlignment(Qt.AlignCenter)
        self.label_12 = QLabel(self.history_landing_distribution)
        self.label_12.setObjectName(u"label_12")
        self.label_12.setGeometry(QRect(0, 180, 111, 20))
        self.label_12.setFont(font2)
        self.label_12.setStyleSheet(u"font-weight:normal")
        self.label_12.setAlignment(Qt.AlignCenter)
        self.statistic_type = QComboBox(self.history_landing_distribution)
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.addItem("")
        self.statistic_type.setObjectName(u"statistic_type")
        self.statistic_type.setGeometry(QRect(5, 200, 101, 22))
        self.statistic_type.setStyleSheet(u"font-weight:normal")
        self.groupBox = QGroupBox(Form)
        self.groupBox.setObjectName(u"groupBox")
        self.groupBox.setGeometry(QRect(460, 0, 341, 51))
        font4 = QFont()
        font4.setBold(True)
        self.groupBox.setFont(font4)
        self.filename = QLineEdit(self.groupBox)
        self.filename.setObjectName(u"filename")
        self.filename.setGeometry(QRect(10, 20, 251, 21))
        self.filename.setStyleSheet(u"font-weight:normal")
        self.filename.setReadOnly(False)
        self.load_file = QPushButton(self.groupBox)
        self.load_file.setObjectName(u"load_file")
        self.load_file.setGeometry(QRect(270, 20, 61, 21))
        self.load_file.setStyleSheet(u"font-weight:normal")
        self.label_9 = QLabel(Form)
        self.label_9.setObjectName(u"label_9")
        self.label_9.setGeometry(QRect(470, 110, 231, 16))
        font5 = QFont()
        font5.setPointSize(10)
        font5.setBold(True)
        self.label_9.setFont(font5)
        self.lose_state_table.raise_()
        self.type_distribution.raise_()
        self.label.raise_()
        self.playerB_y.raise_()
        self.label_5.raise_()
        self.label_4.raise_()
        self.ball_round_id.raise_()
        self.auto_nextball.raise_()
        self.nextball.raise_()
        self.landing_y.raise_()
        self.ball_round_choose.raise_()
        self.label_6.raise_()
        self.playerA_x.raise_()
        self.groupBox_2.raise_()
        self.lose_reason_pie.raise_()
        self.history_landing_distribution.raise_()
        self.groupBox.raise_()
        self.label_9.raise_()
        self.playerA_y.raise_()
        self.landing_x.raise_()
        self.playerB_x.raise_()

        self.retranslateUi(Form)

        QMetaObject.connectSlotsByName(Form)
    # setupUi

    def retranslateUi(self, Form):
        Form.setWindowTitle(QCoreApplication.translate("Form", u"Form", None))
        self.label.setText(QCoreApplication.translate("Form", u"Replay", None))
        self.playerB_y.setText(QCoreApplication.translate("Form", u"0", None))
        self.landing_x.setText(QCoreApplication.translate("Form", u"0", None))
        self.label_5.setText(QCoreApplication.translate("Form", u"Player B Pos.", None))
        self.label_4.setText(QCoreApplication.translate("Form", u"Player A Pos.", None))
        self.playerB_x.setText(QCoreApplication.translate("Form", u"0", None))
        self.ball_round_id.setText(QCoreApplication.translate("Form", u"0", None))
        self.auto_nextball.setText(QCoreApplication.translate("Form", u"Auto", None))
        self.nextball.setText(QCoreApplication.translate("Form", u"Next Ball", None))
        self.landing_y.setText(QCoreApplication.translate("Form", u"0", None))
        self.label_6.setText(QCoreApplication.translate("Form", u"Ball Pos.", None))
        self.type_distribution.setText(QCoreApplication.translate("Form", u"(shot type distribution)", None))
        self.playerA_y.setText(QCoreApplication.translate("Form", u"0", None))
        self.playerA_x.setText(QCoreApplication.translate("Form", u"0", None))
        ___qtablewidgetitem = self.lose_state_table.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("Form", u"1", None));
        ___qtablewidgetitem1 = self.lose_state_table.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("Form", u"2", None));
        ___qtablewidgetitem2 = self.lose_state_table.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("Form", u"3", None));
        ___qtablewidgetitem3 = self.lose_state_table.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("Form", u"4", None));
        ___qtablewidgetitem4 = self.lose_state_table.verticalHeaderItem(0)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("Form", u"Player", None));
        ___qtablewidgetitem5 = self.lose_state_table.verticalHeaderItem(1)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("Form", u"Opponent", None));
        ___qtablewidgetitem6 = self.lose_state_table.verticalHeaderItem(2)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("Form", u"Ball", None));
        ___qtablewidgetitem7 = self.lose_state_table.verticalHeaderItem(3)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("Form", u"Lose Reason", None));
        ___qtablewidgetitem8 = self.lose_state_table.verticalHeaderItem(4)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("Form", u"Percent", None));

        __sortingEnabled = self.lose_state_table.isSortingEnabled()
        self.lose_state_table.setSortingEnabled(False)
        self.lose_state_table.setSortingEnabled(__sortingEnabled)

        self.groupBox_2.setTitle("")
        self.A_win_ratio.setText(QCoreApplication.translate("Form", u"A Win Rate: 50% -> 75%", None))
        self.B_win_ratio.setText(QCoreApplication.translate("Form", u"B Win Rate: 50%  -> 25%", None))
        self.save.setText(QCoreApplication.translate("Form", u"Save", None))
        self.lose_reason_pie.setText(QCoreApplication.translate("Form", u"(lose reason pie)", None))
        self.history_landing_distribution.setTitle(QCoreApplication.translate("Form", u"History Distribution", None))
        self.statistic_ball.setItemText(0, QCoreApplication.translate("Form", u"1", None))
        self.statistic_ball.setItemText(1, QCoreApplication.translate("Form", u"2", None))
        self.statistic_ball.setItemText(2, QCoreApplication.translate("Form", u"3", None))
        self.statistic_ball.setItemText(3, QCoreApplication.translate("Form", u"4", None))
        self.statistic_ball.setItemText(4, QCoreApplication.translate("Form", u"5", None))
        self.statistic_ball.setItemText(5, QCoreApplication.translate("Form", u"6", None))
        self.statistic_ball.setItemText(6, QCoreApplication.translate("Form", u"7", None))
        self.statistic_ball.setItemText(7, QCoreApplication.translate("Form", u"8", None))
        self.statistic_ball.setItemText(8, QCoreApplication.translate("Form", u"9", None))
        self.statistic_ball.setItemText(9, QCoreApplication.translate("Form", u"10", None))
        self.statistic_ball.setItemText(10, QCoreApplication.translate("Form", u"All", None))

        self.label_7.setText(QCoreApplication.translate("Form", u"Opponent Location", None))
        self.statistic_player.setItemText(0, QCoreApplication.translate("Form", u"1", None))
        self.statistic_player.setItemText(1, QCoreApplication.translate("Form", u"2", None))
        self.statistic_player.setItemText(2, QCoreApplication.translate("Form", u"3", None))
        self.statistic_player.setItemText(3, QCoreApplication.translate("Form", u"4", None))
        self.statistic_player.setItemText(4, QCoreApplication.translate("Form", u"5", None))
        self.statistic_player.setItemText(5, QCoreApplication.translate("Form", u"6", None))
        self.statistic_player.setItemText(6, QCoreApplication.translate("Form", u"7", None))
        self.statistic_player.setItemText(7, QCoreApplication.translate("Form", u"8", None))
        self.statistic_player.setItemText(8, QCoreApplication.translate("Form", u"9", None))
        self.statistic_player.setItemText(9, QCoreApplication.translate("Form", u"10", None))
        self.statistic_player.setItemText(10, QCoreApplication.translate("Form", u"All", None))

        self.statistic_opponent.setItemText(0, QCoreApplication.translate("Form", u"1", None))
        self.statistic_opponent.setItemText(1, QCoreApplication.translate("Form", u"2", None))
        self.statistic_opponent.setItemText(2, QCoreApplication.translate("Form", u"3", None))
        self.statistic_opponent.setItemText(3, QCoreApplication.translate("Form", u"4", None))
        self.statistic_opponent.setItemText(4, QCoreApplication.translate("Form", u"5", None))
        self.statistic_opponent.setItemText(5, QCoreApplication.translate("Form", u"6", None))
        self.statistic_opponent.setItemText(6, QCoreApplication.translate("Form", u"7", None))
        self.statistic_opponent.setItemText(7, QCoreApplication.translate("Form", u"8", None))
        self.statistic_opponent.setItemText(8, QCoreApplication.translate("Form", u"9", None))
        self.statistic_opponent.setItemText(9, QCoreApplication.translate("Form", u"10", None))
        self.statistic_opponent.setItemText(10, QCoreApplication.translate("Form", u"All", None))

        self.label_2.setText(QCoreApplication.translate("Form", u"Player Location", None))
        self.label_8.setText(QCoreApplication.translate("Form", u"Ball Location", None))
        self.history_landing_image.setText(QCoreApplication.translate("Form", u"(landing distribution)", None))
        self.history_landing_update.setText(QCoreApplication.translate("Form", u"Update", None))
        self.history_movement_image.setText(QCoreApplication.translate("Form", u"(movement distribution)", None))
        self.label_10.setText(QCoreApplication.translate("Form", u"Landing Distribution", None))
        self.label_11.setText(QCoreApplication.translate("Form", u"Moving Distribution", None))
        self.label_12.setText(QCoreApplication.translate("Form", u"Opponent Shot Type", None))
        self.statistic_type.setItemText(0, QCoreApplication.translate("Form", u"Receiving", None))
        self.statistic_type.setItemText(1, QCoreApplication.translate("Form", u"Short Service", None))
        self.statistic_type.setItemText(2, QCoreApplication.translate("Form", u"Clear", None))
        self.statistic_type.setItemText(3, QCoreApplication.translate("Form", u"Push", None))
        self.statistic_type.setItemText(4, QCoreApplication.translate("Form", u"Smash", None))
        self.statistic_type.setItemText(5, QCoreApplication.translate("Form", u"Return Net", None))
        self.statistic_type.setItemText(6, QCoreApplication.translate("Form", u"Drive", None))
        self.statistic_type.setItemText(7, QCoreApplication.translate("Form", u"Net Shot", None))
        self.statistic_type.setItemText(8, QCoreApplication.translate("Form", u"Lob", None))
        self.statistic_type.setItemText(9, QCoreApplication.translate("Form", u"Drop", None))
        self.statistic_type.setItemText(10, QCoreApplication.translate("Form", u"Long Service", None))
        self.statistic_type.setItemText(11, QCoreApplication.translate("Form", u"Cannot Reach", None))
        self.statistic_type.setItemText(12, QCoreApplication.translate("Form", u"All", None))

        self.groupBox.setTitle(QCoreApplication.translate("Form", u"File", None))
        self.load_file.setText(QCoreApplication.translate("Form", u"Import", None))
        self.label_9.setText(QCoreApplication.translate("Form", u"Statistics on Reasons for Losing Points", None))
    # retranslateUi

