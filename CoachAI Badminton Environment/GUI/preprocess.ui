<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>410</width>
    <height>401</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLineEdit" name="directory">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>39</y>
     <width>251</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QPushButton" name="load_directory">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>41</y>
     <width>16</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>22</x>
     <y>40</y>
     <width>101</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>data directory</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>80</y>
     <width>111</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>homography file</string>
   </property>
  </widget>
  <widget class="QPushButton" name="load_homography">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>81</y>
     <width>16</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="homography_filename">
   <property name="geometry">
    <rect>
     <x>138</x>
     <y>79</y>
     <width>251</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="list_filename">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>118</y>
     <width>251</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>22</x>
     <y>119</y>
     <width>61</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>index file</string>
   </property>
  </widget>
  <widget class="QPushButton" name="load_list">
   <property name="geometry">
    <rect>
     <x>370</x>
     <y>120</y>
     <width>16</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>22</x>
     <y>161</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>output filename</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="output_filename">
   <property name="geometry">
    <rect>
     <x>140</x>
     <y>160</y>
     <width>251</width>
     <height>21</height>
    </rect>
   </property>
  </widget>
  <widget class="QProgressBar" name="progressBar">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>230</y>
     <width>351</width>
     <height>23</height>
    </rect>
   </property>
   <property name="value">
    <number>0</number>
   </property>
   <property name="format">
    <string>%p%</string>
   </property>
  </widget>
  <widget class="QPushButton" name="confirm">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>270</y>
     <width>75</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>confirm</string>
   </property>
  </widget>
  <widget class="QLabel" name="error_message">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>270</y>
     <width>241</width>
     <height>20</height>
    </rect>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="textFormat">
    <enum>Qt::AutoText</enum>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="progress_message">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>210</y>
     <width>311</width>
     <height>20</height>
    </rect>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="textFormat">
    <enum>Qt::AutoText</enum>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
   </property>
  </widget>
  <zorder>homography_filename</zorder>
  <zorder>directory</zorder>
  <zorder>load_directory</zorder>
  <zorder>label</zorder>
  <zorder>label_2</zorder>
  <zorder>load_homography</zorder>
  <zorder>list_filename</zorder>
  <zorder>label_3</zorder>
  <zorder>load_list</zorder>
  <zorder>label_4</zorder>
  <zorder>output_filename</zorder>
  <zorder>progressBar</zorder>
  <zorder>confirm</zorder>
  <zorder>error_message</zorder>
  <zorder>progress_message</zorder>
 </widget>
 <resources/>
 <connections/>
</ui>
