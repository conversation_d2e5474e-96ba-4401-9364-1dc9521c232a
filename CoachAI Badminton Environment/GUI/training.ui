<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>476</width>
    <height>447</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLineEdit" name="training_code">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>40</y>
     <width>301</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>40</y>
     <width>81</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>training code</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>70</y>
     <width>61</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>traing data</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="training_data">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>70</y>
     <width>301</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLineEdit" name="output_path">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>100</y>
     <width>301</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_3">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>100</y>
     <width>71</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>output path</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>140</y>
     <width>81</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>max iteration</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="max_iteration">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>140</y>
     <width>71</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>100</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="learning_rate">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>180</y>
     <width>71</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0.001</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>180</y>
     <width>81</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>learning rate</string>
   </property>
  </widget>
  <widget class="QPushButton" name="confirm">
   <property name="geometry">
    <rect>
     <x>310</x>
     <y>220</y>
     <width>75</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>train</string>
   </property>
  </widget>
  <widget class="QPushButton" name="load_training_code">
   <property name="geometry">
    <rect>
     <x>381</x>
     <y>41</y>
     <width>19</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="QPushButton" name="load_training_data">
   <property name="geometry">
    <rect>
     <x>381</x>
     <y>71</y>
     <width>19</width>
     <height>18</height>
    </rect>
   </property>
   <property name="text">
    <string>...</string>
   </property>
  </widget>
  <widget class="QLabel" name="error_message">
   <property name="geometry">
    <rect>
     <x>112</x>
     <y>220</y>
     <width>191</width>
     <height>20</height>
    </rect>
   </property>
   <property name="layoutDirection">
    <enum>Qt::LeftToRight</enum>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
