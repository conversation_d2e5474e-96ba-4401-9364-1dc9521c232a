<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>470</width>
    <height>466</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QGroupBox" name="model1_choose">
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>10</y>
     <width>371</width>
     <height>80</height>
    </rect>
   </property>
   <property name="title">
    <string>Agent 1</string>
   </property>
   <widget class="QRadioButton" name="model1_ShuttleNet">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>20</y>
      <width>161</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Default agent </string>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QRadioButton" name="model1_custom">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>50</y>
      <width>91</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Other agent</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="model1_custom_path">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>50</y>
      <width>251</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QToolButton" name="model1_load_custom">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>340</x>
      <y>52</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>...</string>
    </property>
   </widget>
   <widget class="QComboBox" name="model1_ShuttleNet_player">
    <property name="geometry">
     <rect>
      <x>200</x>
      <y>20</y>
      <width>161</width>
      <height>22</height>
     </rect>
    </property>
    <item>
     <property name="text">
      <string>(choose the opponent)</string>
     </property>
    </item>
   </widget>
  </widget>
  <widget class="QGroupBox" name="model2_choose">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>30</x>
     <y>100</y>
     <width>371</width>
     <height>80</height>
    </rect>
   </property>
   <property name="title">
    <string>Agent 2</string>
   </property>
   <widget class="QRadioButton" name="model2_ShuttleNet">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>20</y>
      <width>161</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Default agent </string>
    </property>
    <property name="checked">
     <bool>true</bool>
    </property>
   </widget>
   <widget class="QRadioButton" name="model2_custom">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>50</y>
      <width>91</width>
      <height>20</height>
     </rect>
    </property>
    <property name="text">
     <string>Other agent</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="model2_custom_path">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>50</y>
      <width>251</width>
      <height>20</height>
     </rect>
    </property>
   </widget>
   <widget class="QToolButton" name="model2_load_custom">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>340</x>
      <y>52</y>
      <width>16</width>
      <height>16</height>
     </rect>
    </property>
    <property name="text">
     <string>...</string>
    </property>
   </widget>
   <widget class="QComboBox" name="model2_ShuttleNet_player">
    <property name="geometry">
     <rect>
      <x>200</x>
      <y>20</y>
      <width>161</width>
      <height>22</height>
     </rect>
    </property>
    <item>
     <property name="text">
      <string>(choose the opponent)</string>
     </property>
    </item>
   </widget>
  </widget>
  <widget class="QPushButton" name="confirm">
   <property name="geometry">
    <rect>
     <x>320</x>
     <y>280</y>
     <width>75</width>
     <height>24</height>
    </rect>
   </property>
   <property name="text">
    <string>generate</string>
   </property>
  </widget>
  <widget class="QLabel" name="error_message">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>240</y>
     <width>201</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
   </property>
  </widget>
  <widget class="QLineEdit" name="output_filename">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>200</y>
     <width>281</width>
     <height>20</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>190</y>
     <width>53</width>
     <height>41</height>
    </rect>
   </property>
   <property name="text">
    <string>Ouput
Filename</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>240</y>
     <width>71</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>Rally Count</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="rally_count">
   <property name="geometry">
    <rect>
     <x>120</x>
     <y>240</y>
     <width>61</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>1000</string>
   </property>
  </widget>
  <widget class="QProgressBar" name="progressBar">
   <property name="geometry">
    <rect>
     <x>50</x>
     <y>280</y>
     <width>261</width>
     <height>23</height>
    </rect>
   </property>
   <property name="value">
    <number>0</number>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
