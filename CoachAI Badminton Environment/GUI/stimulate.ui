<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>818</width>
    <height>621</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>340</y>
     <width>41</width>
     <height>16</height>
    </rect>
   </property>
   <property name="text">
    <string>Replay</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="playerB_y">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>550</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLineEdit" name="landing_x">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>500</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLabel" name="label_5">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>530</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>Player B Pos.</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="label_4">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>430</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>Player A Pos.</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLineEdit" name="playerB_x">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>550</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLineEdit" name="ball_round_id">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>370</y>
     <width>71</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QCheckBox" name="auto_nextball">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>370</y>
     <width>51</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>Auto</string>
   </property>
  </widget>
  <widget class="QPushButton" name="nextball">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>370</y>
     <width>71</width>
     <height>21</height>
    </rect>
   </property>
   <property name="text">
    <string>Next Ball</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="landing_y">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>500</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QSlider" name="ball_round_choose">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>340</y>
     <width>171</width>
     <height>21</height>
    </rect>
   </property>
   <property name="orientation">
    <enum>Qt::Horizontal</enum>
   </property>
  </widget>
  <widget class="QLabel" name="label_6">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>480</y>
     <width>91</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>Ball Pos.</string>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLabel" name="type_distribution">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>10</y>
     <width>441</width>
     <height>311</height>
    </rect>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>(shot type distribution)</string>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QLineEdit" name="playerA_y">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>450</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QLineEdit" name="playerA_x">
   <property name="geometry">
    <rect>
     <x>200</x>
     <y>450</y>
     <width>41</width>
     <height>20</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
   <property name="readOnly">
    <bool>true</bool>
   </property>
  </widget>
  <widget class="QTableWidget" name="lose_state_table">
   <property name="geometry">
    <rect>
     <x>460</x>
     <y>130</y>
     <width>331</width>
     <height>171</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>8</pointsize>
    </font>
   </property>
   <property name="verticalScrollBarPolicy">
    <enum>Qt::ScrollBarAlwaysOff</enum>
   </property>
   <property name="horizontalScrollBarPolicy">
    <enum>Qt::ScrollBarAlwaysOff</enum>
   </property>
   <attribute name="horizontalHeaderMinimumSectionSize">
    <number>30</number>
   </attribute>
   <attribute name="horizontalHeaderDefaultSectionSize">
    <number>63</number>
   </attribute>
   <attribute name="verticalHeaderMinimumSectionSize">
    <number>30</number>
   </attribute>
   <attribute name="verticalHeaderShowSortIndicator" stdset="0">
    <bool>false</bool>
   </attribute>
   <row>
    <property name="text">
     <string>Player</string>
    </property>
   </row>
   <row>
    <property name="text">
     <string>Opponent</string>
    </property>
   </row>
   <row>
    <property name="text">
     <string>Ball</string>
    </property>
   </row>
   <row>
    <property name="text">
     <string>Lose Reason</string>
    </property>
   </row>
   <row>
    <property name="text">
     <string>Percent</string>
    </property>
   </row>
   <column>
    <property name="text">
     <string>1</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>2</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>3</string>
    </property>
   </column>
   <column>
    <property name="text">
     <string>4</string>
    </property>
   </column>
   <item row="2" column="0">
    <property name="text">
     <string/>
    </property>
    <property name="textAlignment">
     <set>AlignCenter</set>
    </property>
   </item>
  </widget>
  <widget class="QGroupBox" name="groupBox_2">
   <property name="geometry">
    <rect>
     <x>460</x>
     <y>60</y>
     <width>341</width>
     <height>41</height>
    </rect>
   </property>
   <property name="title">
    <string/>
   </property>
   <widget class="QLabel" name="A_win_ratio">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>0</y>
      <width>181</width>
      <height>21</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>11</pointsize>
     </font>
    </property>
    <property name="text">
     <string>A Win Rate: 50% -&gt; 75%</string>
    </property>
   </widget>
   <widget class="QLabel" name="B_win_ratio">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>181</width>
      <height>21</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>11</pointsize>
     </font>
    </property>
    <property name="text">
     <string>B Win Rate: 50%  -&gt; 25%</string>
    </property>
   </widget>
   <widget class="QPushButton" name="save">
    <property name="geometry">
     <rect>
      <x>270</x>
      <y>10</y>
      <width>61</width>
      <height>21</height>
     </rect>
    </property>
    <property name="text">
     <string>Save</string>
    </property>
   </widget>
  </widget>
  <widget class="QLabel" name="lose_reason_pie">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>420</y>
     <width>151</width>
     <height>191</height>
    </rect>
   </property>
   <property name="autoFillBackground">
    <bool>false</bool>
   </property>
   <property name="styleSheet">
    <string notr="true"/>
   </property>
   <property name="text">
    <string>(lose reason pie)</string>
   </property>
   <property name="scaledContents">
    <bool>true</bool>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
  <widget class="QGroupBox" name="history_landing_distribution">
   <property name="geometry">
    <rect>
     <x>250</x>
     <y>320</y>
     <width>551</width>
     <height>281</height>
    </rect>
   </property>
   <property name="styleSheet">
    <string notr="true">font-weight:bold;</string>
   </property>
   <property name="title">
    <string>History Distribution</string>
   </property>
   <widget class="QComboBox" name="statistic_ball">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>150</y>
      <width>91</width>
      <height>22</height>
     </rect>
    </property>
    <property name="layoutDirection">
     <enum>Qt::LeftToRight</enum>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <item>
     <property name="text">
      <string>1</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>2</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>3</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>4</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>5</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>6</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>7</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>8</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>9</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>10</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>All</string>
     </property>
    </item>
   </widget>
   <widget class="QLabel" name="label_7">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>80</y>
      <width>111</width>
      <height>20</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <bold>false</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Opponent Location</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QComboBox" name="statistic_player">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>50</y>
      <width>91</width>
      <height>22</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <item>
     <property name="text">
      <string>1</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>2</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>3</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>4</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>5</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>6</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>7</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>8</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>9</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>10</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>All</string>
     </property>
    </item>
   </widget>
   <widget class="QComboBox" name="statistic_opponent">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>100</y>
      <width>91</width>
      <height>22</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <item>
     <property name="text">
      <string>1</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>2</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>3</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>4</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>5</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>6</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>7</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>8</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>9</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>10</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>All</string>
     </property>
    </item>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>30</y>
      <width>111</width>
      <height>20</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <bold>false</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Player Location</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="label_8">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>130</y>
      <width>111</width>
      <height>20</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <bold>false</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Ball Location</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="history_landing_image">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>50</y>
      <width>221</width>
      <height>231</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
    <property name="text">
     <string>(landing distribution)</string>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QPushButton" name="history_landing_update">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>250</y>
      <width>91</width>
      <height>24</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Update</string>
    </property>
   </widget>
   <widget class="QLabel" name="history_movement_image">
    <property name="geometry">
     <rect>
      <x>330</x>
      <y>50</y>
      <width>221</width>
      <height>231</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true"/>
    </property>
    <property name="text">
     <string>(movement distribution)</string>
    </property>
    <property name="scaledContents">
     <bool>false</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="label_10">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>10</y>
      <width>221</width>
      <height>21</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <bold>true</bold>
     </font>
    </property>
    <property name="text">
     <string>Landing Distribution</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="label_11">
    <property name="geometry">
     <rect>
      <x>330</x>
      <y>10</y>
      <width>221</width>
      <height>20</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
      <bold>true</bold>
     </font>
    </property>
    <property name="text">
     <string>Moving Distribution</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QLabel" name="label_12">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>180</y>
      <width>111</width>
      <height>20</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>8</pointsize>
      <bold>false</bold>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Opponent Shot Type</string>
    </property>
    <property name="alignment">
     <set>Qt::AlignCenter</set>
    </property>
   </widget>
   <widget class="QComboBox" name="statistic_type">
    <property name="geometry">
     <rect>
      <x>5</x>
      <y>200</y>
      <width>101</width>
      <height>22</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <item>
     <property name="text">
      <string>Receiving</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Short Service</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Clear</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Push</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Smash</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Return Net</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Drive</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Net Shot</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Lob</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Drop</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Long Service</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>Cannot Reach</string>
     </property>
    </item>
    <item>
     <property name="text">
      <string>All</string>
     </property>
    </item>
   </widget>
  </widget>
  <widget class="QGroupBox" name="groupBox">
   <property name="geometry">
    <rect>
     <x>460</x>
     <y>0</y>
     <width>341</width>
     <height>51</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <bold>true</bold>
    </font>
   </property>
   <property name="title">
    <string>File</string>
   </property>
   <widget class="QLineEdit" name="filename">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>20</y>
      <width>251</width>
      <height>21</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="readOnly">
     <bool>false</bool>
    </property>
   </widget>
   <widget class="QPushButton" name="load_file">
    <property name="geometry">
     <rect>
      <x>270</x>
      <y>20</y>
      <width>61</width>
      <height>21</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">font-weight:normal</string>
    </property>
    <property name="text">
     <string>Import</string>
    </property>
   </widget>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>470</x>
     <y>110</y>
     <width>231</width>
     <height>16</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Statistics on Reasons for Losing Points</string>
   </property>
  </widget>
  <zorder>lose_state_table</zorder>
  <zorder>type_distribution</zorder>
  <zorder>label</zorder>
  <zorder>playerB_y</zorder>
  <zorder>label_5</zorder>
  <zorder>label_4</zorder>
  <zorder>ball_round_id</zorder>
  <zorder>auto_nextball</zorder>
  <zorder>nextball</zorder>
  <zorder>landing_y</zorder>
  <zorder>ball_round_choose</zorder>
  <zorder>label_6</zorder>
  <zorder>playerA_x</zorder>
  <zorder>groupBox_2</zorder>
  <zorder>lose_reason_pie</zorder>
  <zorder>history_landing_distribution</zorder>
  <zorder>groupBox</zorder>
  <zorder>label_9</zorder>
  <zorder>playerA_y</zorder>
  <zorder>landing_x</zorder>
  <zorder>playerB_x</zorder>
 </widget>
 <resources/>
 <connections/>
</ui>
