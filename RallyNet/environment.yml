name: badminton
channels:
  - pytorch
  - dglteam
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_kmp_llvm
  - asttokens=2.0.5=pyhd3eb1b0_0
  - backcall=0.2.0=pyhd3eb1b0_0
  - blas=1.0=mkl
  - bottleneck=1.3.5=py38h7deecbd_0
  - brotli=1.0.9=h5eee18b_7
  - brotli-bin=1.0.9=h5eee18b_7
  - brotlipy=0.7.0=py38h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.10.11=h06a4308_0
  - certifi=2022.9.24=py38h06a4308_0
  - cffi=1.15.1=py38h74dc2b5_0
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cryptography=38.0.1=py38h9ce1e76_0
  - cuda=11.7.1=0
  - cuda-cccl=11.7.91=0
  - cuda-command-line-tools=11.7.1=0
  - cuda-compiler=11.7.1=0
  - cuda-cudart=11.7.99=0
  - cuda-cudart-dev=11.7.99=0
  - cuda-cuobjdump=11.7.91=0
  - cuda-cupti=11.7.101=0
  - cuda-cuxxfilt=11.7.91=0
  - cuda-demo-suite=11.8.86=0
  - cuda-documentation=11.8.86=0
  - cuda-driver-dev=11.7.99=0
  - cuda-gdb=11.8.86=0
  - cuda-libraries=11.7.1=0
  - cuda-libraries-dev=11.7.1=0
  - cuda-memcheck=11.8.86=0
  - cuda-nsight=11.8.86=0
  - cuda-nsight-compute=11.8.0=0
  - cuda-nvcc=11.7.99=0
  - cuda-nvdisasm=11.8.86=0
  - cuda-nvml-dev=11.7.91=0
  - cuda-nvprof=11.8.87=0
  - cuda-nvprune=11.7.91=0
  - cuda-nvrtc=11.7.99=0
  - cuda-nvrtc-dev=11.7.99=0
  - cuda-nvtx=11.7.91=0
  - cuda-nvvp=11.8.87=0
  - cuda-runtime=11.7.1=0
  - cuda-sanitizer-api=11.8.86=0
  - cuda-toolkit=11.7.1=0
  - cuda-tools=11.7.1=0
  - cuda-visual-tools=11.7.1=0
  - cudatoolkit=11.3.1=h2bc3f7f_2
  - cycler=0.11.0=pyhd3eb1b0_0
  - debugpy=1.5.1=py38h295c915_0
  - decorator=5.1.1=pyhd3eb1b0_0
  - dgl-cuda11.6=0.9.1post1=py38_0
  - dython=0.7.2=pyhd8ed1ab_0
  - entrypoints=0.4=py38h06a4308_0
  - executing=0.8.3=pyhd3eb1b0_0
  - ffmpeg=4.2.2=h20bf706_0
  - fftw=3.3.10=nompi_h77c792f_102
  - freetype=2.12.1=h4a9f257_0
  - gds-tools=********=0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - idna=3.4=py38h06a4308_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - ipykernel=6.15.2=py38h06a4308_0
  - ipython=8.6.0=py38h06a4308_0
  - jedi=0.18.1=py38h06a4308_1
  - joblib=1.2.0=pyhd8ed1ab_0
  - jpeg=9e=h7f8727e_0
  - jupyter_client=7.4.7=py38h06a4308_0
  - jupyter_core=4.11.2=py38h06a4308_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libbrotlicommon=1.0.9=h5eee18b_7
  - libbrotlidec=1.0.9=h5eee18b_7
  - libbrotlienc=1.0.9=h5eee18b_7
  - libcublas=*********=0
  - libcublas-dev=*********=0
  - libcufft=*********=0
  - libcufft-dev=*********=0
  - libcufile=********=0
  - libcufile-dev=********=0
  - libcurand=*********=0
  - libcurand-dev=*********=0
  - libcusolver=*********=0
  - libcusolver-dev=*********=0
  - libcusparse=*********=0
  - libcusparse-dev=*********=0
  - libdeflate=1.8=h7f8727e_5
  - libffi=3.3=he6710b0_2
  - libgcc-ng=12.2.0=h65d4601_19
  - libgfortran-ng=12.2.0=h69a702a_19
  - libgfortran5=12.2.0=h337968e_19
  - libidn2=2.3.2=h7f8727e_0
  - libnpp=*********=0
  - libnpp-dev=*********=0
  - libnvjpeg=11.9.0.86=0
  - libnvjpeg-dev=11.9.0.86=0
  - libopus=1.3.1=h7b6447c_0
  - libpng=1.6.37=hbc83047_0
  - libsodium=1.0.18=h7b6447c_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.4.0=hecacb30_2
  - libunistring=0.9.10=h27cfd23_0
  - libvpx=1.7.0=h439df22_0
  - libwebp=1.2.4=h11a3e52_0
  - libwebp-base=1.2.4=h5eee18b_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.3=h295c915_1
  - matplotlib-base=3.5.3=py38hf590b9c_0
  - matplotlib-inline=0.1.6=py38h06a4308_0
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.1=py38hd3c417c_0
  - mkl_random=1.2.2=py38h51133e4_0
  - munkres=1.1.4=py_0
  - ncurses=6.3=h5eee18b_3
  - nest-asyncio=1.5.5=py38h06a4308_0
  - nettle=3.7.3=hbbd107a_1
  - nsight-compute=2022.3.0.22=0
  - numexpr=2.8.4=py38he184ba9_0
  - numpy=1.23.4=py38h14f4228_0
  - numpy-base=1.23.4=py38h31eccc5_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1s=h7f8727e_0
  - packaging=21.3=pyhd3eb1b0_0
  - parso=0.8.3=pyhd3eb1b0_0
  - pexpect=4.8.0=pyhd3eb1b0_3
  - pickleshare=0.7.5=pyhd3eb1b0_1003
  - pillow=9.2.0=py38hace64e9_1
  - pip=22.2.2=py38h06a4308_0
  - prompt-toolkit=3.0.20=pyhd3eb1b0_0
  - psutil=5.9.4=py38h0a891b7_0
  - ptyprocess=0.7.0=pyhd3eb1b0_2
  - pure_eval=0.2.2=pyhd3eb1b0_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.11.2=pyhd3eb1b0_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pyparsing=3.0.9=py38h06a4308_0
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.13=haa1d7c7_1
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python_abi=3.8=2_cp38
  - pytorch=1.13.0=py3.8_cuda11.7_cudnn8.5.0_0
  - pytorch-cuda=11.7=h67b0de4_0
  - pytorch-mutex=1.0=cuda
  - pyzmq=23.2.0=py38h6a678d5_0
  - readline=8.2=h5eee18b_0
  - requests=2.28.1=py38h06a4308_0
  - scikit-learn=1.1.3=py38h6a678d5_0
  - scikit-plot=0.3.7=py_1
  - seaborn=0.12.0=py38h06a4308_0
  - setuptools=65.5.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.39.3=h5082296_0
  - stack_data=0.2.0=pyhd3eb1b0_0
  - threadpoolctl=3.1.0=pyh8a188c0_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=0.13.0=py38_cu117
  - torchvision=0.14.0=py38_cu117
  - tornado=6.2=py38h5eee18b_0
  - tqdm=4.64.1=py38h06a4308_0
  - traitlets=5.1.1=pyhd3eb1b0_0
  - typing_extensions=4.3.0=py38h06a4308_0
  - urllib3=1.26.12=py38h06a4308_0
  - wcwidth=0.2.5=pyhd3eb1b0_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - x264=1!157.20191217=h7b6447c_0
  - xz=5.2.6=h5eee18b_0
  - zeromq=4.3.4=h2531618_0
  - zlib=1.2.13=h5eee18b_0
  - zstd=1.5.2=ha4553b6_0
  - pip:
    - aim==3.14.4
    - aim-ui==3.14.4
    - aimrecords==0.0.7
    - aimrocks==0.2.1
    - aiofiles==22.1.0
    - alembic==1.8.1
    - association-metrics==0.0.1
    - async-generator==1.10
    - attrs==22.1.0
    - base58==2.0.1
    - boltons==21.0.0
    - boto3==1.26.3
    - botocore==1.29.3
    - cachetools==5.2.0
    - click==8.1.3
    - contourpy==1.0.6
    - cython==0.29.32
    - dtaidistance==2.3.10
    - exceptiongroup==1.0.1
    - fastapi==0.67.0
    - filelock==3.8.0
    - fonttools==4.38.0
    - gensim==4.2.0
    - google-images-download==2.8.0
    - greenlet==2.0.1
    - grpcio==1.50.0
    - h11==0.14.0
    - htmlmin==0.1.12
    - huggingface-hub==0.10.1
    - imagehash==4.3.1
    - importlib-metadata==5.0.0
    - importlib-resources==5.10.0
    - ipywidgets==8.0.2
    - jinja2==3.1.2
    - jmespath==1.0.1
    - jsonlines==3.1.0
    - jupyterlab-widgets==3.0.3
    - kiwisolver==1.4.4
    - llvmlite==0.39.1
    - mako==1.2.4
    - markupsafe==2.1.1
    - missingno==0.5.1
    - multimethod==1.9
    - networkx==2.8.8
    - numba==0.56.4
    - opencv-python==********
    - outcome==1.2.0
    - pandas==1.5.1
    - pandas-profiling==3.4.0
    - phik==0.12.2
    - protobuf==3.20.1
    - py3nvml==0.2.7
    - pydantic==1.10.2
    - pymysql==1.0.2
    - pytesseract==0.3.10
    - pytorch-transformers==1.2.0
    - pytz==2022.6
    - pywavelets==1.4.1
    - pyyaml==6.0
    - regex==2022.10.31
    - restrictedpython==6.0
    - s3transfer==0.6.0
    - sacremoses==0.0.53
    - scipy==1.9.3
    - selenium==4.6.0
    - sentencepiece==0.1.97
    - smart-open==6.2.0
    - sniffio==1.3.0
    - sortedcontainers==2.4.0
    - sqlalchemy==1.4.44
    - starlette==0.14.2
    - tangled-up-in-unicode==0.2.0
    - tensorboardx==2.5.1
    - timm==0.6.11
    - tokenizers==0.13.1
    - torchdiffeq==0.2.3
    - torchsde==0.2.5
    - trampoline==0.1.2
    - transformers==4.24.0
    - trio==0.22.0
    - trio-websocket==0.9.2
    - tslearn==0.5.2
    - uvicorn==0.19.0
    - visions==0.7.5
    - widgetsnbextension==4.0.3
    - wsproto==1.2.0
    - xlrd==2.0.1
    - xlwt==1.3.0
    - xmltodict==0.13.0
    - zipp==3.10.0
prefix: /home/<USER>/anaconda3/envs/badminton
